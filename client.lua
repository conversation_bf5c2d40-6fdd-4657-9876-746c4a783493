print('[KOTH] Client loading...')

-- Register decorator for player vehicles (must be done early)
DecorRegister("PlayerVehicle", 3)

-- TEAM SPAWN COORDS
local teamSpawns = {
  red   = { x=2238.15, y=3788.91, z=35.89, heading=120.5 },
  blue  = { x=1323.77, y=3143.33, z=40.41, heading=282.48 },
  green = { x=1865.99, y=2607.15, z=45.67, heading=276.45 },
}

-- Inform the death system of the initial team spawns.  Without this
-- event, players who die before the first map rotation would respawn at
-- placeholder coordinates defined in client_death.lua.  The death system
-- listens for this event and updates its spawn table accordingly.  It
-- will receive further updates when a map rotates.
-- Do not immediately trigger the updateTeamSpawns event here.  The server
-- will send the correct spawn configuration on clientReady, and the
-- handler registered below will update this table and respawn peds
-- accordingly.  Removing this early event prevents clients from
-- inadvertently using the placeholder spawns defined here.

-- Player variables
local playerTeam = nil
-- Track the currently selected class.  Defaults to assault so players
-- always spawn with the assault outfit until they choose another class.
local currentClass = 'assault'
local playerStats = nil
local hasSelectedTeam = false
-- Flag indicating a pending team selection request has been sent to the server.
-- While this is true we suppress the team selection UI when the player
-- respawns to avoid flickering or prematurely reverting to selection.
local pendingTeamSelection = false

-- Track the prestige ped entity so we can clean it up on map rotation
local prestigePed = nil

--
-- Damage tracking for friendly fire mitigation
--
-- We keep a small cache of each player's last known health and armour
-- values.  When the damage event fires and both victim and attacker
-- are on the same team, we restore the victim's health/armour from
-- this cache to effectively negate any friendly fire.  Updating
-- frequently ensures we have up‑to‑date values for all players.
local lastHealths = {}

Citizen.CreateThread(function()
  while true do
    -- Update health/armour for all active players
    for _, playerId in ipairs(GetActivePlayers()) do
      local ped = GetPlayerPed(playerId)
      if ped and DoesEntityExist(ped) then
        lastHealths[ped] = {
          health = GetEntityHealth(ped),
          armour = GetPedArmour(ped)
        }
      end
    end
    -- Poll every 50ms to keep values reasonably up to date
    Citizen.Wait(50)
  end
end)



-- Helper functions to manage NUI focus while preserving game input.
-- When opening NUI, call openUIFocus() to allow players to still access the pause menu (ESC) and map while the UI is open.
-- When closing NUI, call closeUIFocus() to fully return control to the game.
local function openUIFocus()
  -- Set NUI focus and allow game input (e.g. ESC) to remain active
  SetNuiFocus(true, true)
  SetNuiFocusKeepInput(true)
end

local function closeUIFocus()
  -- Remove NUI focus and restore normal input handling
  SetNuiFocus(false, false)
  SetNuiFocusKeepInput(false)
end







-- Colour mapping for team‑based parachute tints.  These indices map to
-- parachute colour variants defined by GTA.  See documentation for
-- SetPlayerParachuteTintIndex on the FiveM natives site for more
-- details.  We choose 1 for red (dark red), 5 for blue and 2 for
-- green to give each team a distinct chute colour.
local parachuteTintByTeam = {
  red   = 1,
  blue  = 5,
  green = 2,
}

-- Draw 3D text at a world position.  This helper function projects
-- a 3D coordinate to 2D screen space and then renders a string with
-- optional colour.  It returns early if the point is off screen.
local function DrawText3D(x, y, z, text, colour)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  if not onScreen then return end
  SetTextScale(0.35, 0.35)
  SetTextFont(4)
  SetTextProportional(1)
  SetTextColour(colour.r or 255, colour.g or 255, colour.b or 255, 255)
  SetTextEntry("STRING")
  SetTextCentre(true)
  AddTextComponentString(text)
  DrawText(_x, _y)
end

--[[
  Apply team‑coloured parachute tints when the player deploys a
  parachute.  We continuously check if the player is currently
  parachuting.  If so, we set the parachute tint index to a colour
  based on their team.  The mapping is defined in
  parachuteTintByTeam.  This only needs to be set once per frame
  while parachuting, but calling repeatedly is safe.  If the player
  is not parachuting, the function yields.
]]
Citizen.CreateThread(function()
  while true do
    local ped = PlayerPedId()
    -- Only update if we know our team
    if playerTeam and parachuteTintByTeam[playerTeam] then
      if IsPedInParachuteFreeFall(ped) or GetPedParachuteState(ped) ~= -1 then
        local tint = parachuteTintByTeam[playerTeam]
        -- Use both natives to cover different build versions
        SetPlayerParachuteTintIndex(PlayerId(), tint)
        SetPedParachuteTintIndex(ped, tint)
      end
    end
    Citizen.Wait(500)
  end
end)

-- =========================================================================
-- Prestige System Client Events and NUI Callbacks
--
-- The prestige system allows players to reset their level upon reaching
-- level 50 in exchange for prestige tokens.  A prestige ped spawns near
-- each KOTH zone.  Interacting with this ped (pressing E) will open the
-- prestige shop UI.  Within the UI, players can choose to prestige, use
-- their weapon token to permanently unlock a prestige weapon, or use
-- their vehicle token to unlock the prestige vehicle (Insurgent2).  The
-- server validates all actions and persists token balances and unlocked
-- items.  This section handles the client‑side UI and communication with
-- the server.

-- Listen for prestige data from the server.  When the player requests
-- their prestige data (via koth:getPrestigeData) the server responds
-- with koth:receivePrestigeData containing the player's current prestige
-- rank and available tokens.  Use this information to open the prestige
-- menu and populate the UI.
RegisterNetEvent('koth:receivePrestigeData')
AddEventHandler('koth:receivePrestigeData', function(data)
  -- Safely determine the player's current level for UI logic.  If
  -- playerStats is nil (e.g. before first data update) default to level 1.
  local lvl = 1
  if playerStats and playerStats.level then
    lvl = playerStats.level
  end
  -- Send a message to the NUI to open the prestige menu.  Include the
  -- prestige rank, token counts and current level so the UI can enable
  -- or disable options appropriately.
  SendNUIMessage({
    action = 'openPrestigeMenu',
    data = {
      prestigeRank = data.prestigeRank or 0,
      weaponTokens = data.weaponTokens or 0,
      vehicleTokens = data.vehicleTokens or 0,
      playerLevel = lvl
    }
  })
  -- Give the UI focus so the player can click buttons.  Both cursor
  -- and keyboard focus are enabled to allow interaction.
  openUIFocus()
end)

-- Handle actions from the prestige UI.  The UI sends a payload
-- containing an action string and optional parameters.  Based on the
-- action, trigger the appropriate server event to perform the prestige
-- operation.  Afterwards, close the UI and request updated prestige
-- data so the HUD reflects any token changes.
RegisterNUICallback('prestigeAction', function(data, cb)
  if not data or not data.action then
    cb('ok')
    return
  end
  if data.action == 'prestige' then
    -- Player confirmed they wish to prestige.  The server will
    -- validate the level and update the player's XP/level and tokens.
    TriggerServerEvent('koth:prestige')
  elseif data.action == 'buyWeapon' then
    -- Player selected a prestige weapon.  Ensure a weapon id was
    -- provided and forward it to the server.  Allowed weapon IDs are
    -- validated server‑side.
    local weaponId = data.weapon or ''
    TriggerServerEvent('koth:purchasePrestigeWeapon', weaponId)
  elseif data.action == 'buyVehicle' then
    -- Player chose to purchase a prestige vehicle.  The UI
    -- may provide a specific vehicle identifier; if none is
    -- provided, default to Insurgent2 for backward compatibility.
    local veh = data.vehicle or data.vehicleName or 'Insurgent2'
    TriggerServerEvent('koth:purchasePrestigeVehicle', veh)
  end
  -- Close the prestige UI and remove focus.  Do not immediately
  -- request new prestige data here because the server will send
  -- koth:updatePlayerData after a purchase or prestige action to
  -- refresh the HUD.  Re‑opening the prestige menu right away
  -- would result in the UI popping back up after a purchase.
  SendNUIMessage({ action = 'closePrestigeMenu' })
  closeUIFocus()
  -- NUI callbacks always expect a response even if no data is
  -- returned.  Inform the UI that the action has been handled.
  cb('ok')

-- Daily Reward NUI callbacks (client forwards to server via NUI POST)
RegisterNUICallback('claimDaily', function(data, cb)
  TriggerServerEvent('koth:claimDaily')
  cb('ok')
end)

end)


--[[
  Draw coloured nametags over teammates.  This thread iterates over all
  active players and, if they are on the same team as the local
  player, draws their name along with their server ID above their head
  in the colour of that team.  Enemies' names are not drawn.  The colour
  mapping uses the same values as other UI elements.  Only one nametag
  per player is rendered to avoid duplicates.
]]
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if playerTeam then
      local me = PlayerId()
      for _, pid in ipairs(GetActivePlayers()) do
        if pid ~= me then
          local serverId = GetPlayerServerId(pid)
          -- teamMembers is populated by koth:updateTeamMembers event
          local theirTeam = teamMembers and teamMembers[serverId]
          if theirTeam and theirTeam == playerTeam then
            local ped = GetPlayerPed(pid)
            if DoesEntityExist(ped) and not IsPedDeadOrDying(ped, true) then
              local pos = GetEntityCoords(ped) + vector3(0.0, 0.0, 1.2)
              -- Determine colour by team
              local col = { r = 255, g = 255, b = 255 }
              if theirTeam == 'red' then
                col = { r = 255, g = 60, b = 60 }
              elseif theirTeam == 'blue' then
                col = { r = 60, g = 120, b = 255 }
              elseif theirTeam == 'green' then
                col = { r = 60, g = 255, b = 60 }
              end
              -- Compose the display name with the server ID in brackets
              local name = GetPlayerName(pid) or 'Player'
              local display = string.format('%s [%s]', name, tostring(serverId))
              DrawText3D(pos.x, pos.y, pos.z, display, col)
            end
          end
        end
      end
    end
  end
end)
local weaponShopOpened = false

-- Keep track of the player's most recently purchased loadout (weapon and
-- attachments).  When the player dies, a prompt will appear in the
-- spawn area allowing them to repurchase this loadout by pressing G.
-- weapon: the weapon hash name (string), e.g. 'WEAPON_CARBINERIFLE'
-- weaponPrice: the price paid for the weapon
-- attachments: a list of tables with name, component and price fields
local previousLoadout = {
  weapon = nil,
  weaponPrice = 0,
  purchaseType = nil, -- 'buy' or 'rent'
  owned = false,      -- true if permanently owned (last transaction was a buy)
  attachments = {}
}

-- Flag to indicate that the player is currently repurchasing their
-- previous loadout.  When true, the koth:giveWeapon event will not
-- clear the previousLoadout.attachments table.  This allows
-- attachments to persist across repeated repurchases.  The flag is
-- set immediately before triggering a repurchase and cleared once
-- all attachments have been re-applied.
local isRepurchasingLoadout = false

-- ====================================================================
-- DOWNED PLAYER TRACKING
-- Maintain a table of server IDs representing players that are downed
-- (eligible for revival).  This is populated by the server when any
-- player dies and cleared when they are revived or respawned.  Using
-- this table allows the medic revive logic to detect downed players
-- reliably, even though the ped may have been resurrected with
-- NetworkResurrectLocalPlayer and thus IsEntityDead returns false.
local downedPlayers = {}

-- Receive notification that a player has gone down
RegisterNetEvent('koth:addDownedPlayer')
AddEventHandler('koth:addDownedPlayer', function(serverId)
  -- Ignore if the serverId is invalid or refers to the local player
  local id = tonumber(serverId)
  if not id or id <= 0 then return end
  if id == GetPlayerServerId(PlayerId()) then return end
  downedPlayers[id] = true
end)

-- Receive notification that a player has been revived/respawned
RegisterNetEvent('koth:removeDownedPlayer')
AddEventHandler('koth:removeDownedPlayer', function(serverId)
  local id = tonumber(serverId)
  if not id or id <= 0 then return end
  downedPlayers[id] = nil
end)

--
-- MEDIC REVIVE HANDLING
--
-- This thread allows players using the medic class to revive downed
-- teammates.  When a medic approaches a dead player within a small
-- radius, a 3D prompt appears above the body instructing them to
-- press the E key.  Holding E for eight seconds will trigger a
-- server event to resurrect the target on the spot.  A simple
-- progress bar is drawn on the screen while reviving.  The UI and
-- progress handling run entirely on the client for responsiveness; the
-- server validates that the reviver and target are on the same team
-- before completing the revive.
Citizen.CreateThread(function()
  -- Flag to prevent overlapping revive attempts
  local reviving = false
  while true do
    Citizen.Wait(0)
    local playerPed = PlayerPedId()
    -- Only allow revives when the medic is alive
    if not IsEntityDead(playerPed) then
      -- Determine if the current class is medic.  Use the koth_classes
      -- client export to fetch the player's current class.  This
      -- ensures that the value remains in sync with the class system
      -- even if the local currentClass variable is not updated by
      -- class selection events in other resources.
      local classId = nil
      if exports['koth_classes'] and exports['koth_classes'].GetCurrentClass then
        classId = exports['koth_classes']:GetCurrentClass()
      else
        classId = currentClass
      end
      local isMedic = (classId and tostring(classId):lower() == 'medic')
      if isMedic and not reviving then
        local myCoords = GetEntityCoords(playerPed)
        -- Look through the list of downed players and find one within range
        for serverId, _ in pairs(downedPlayers) do
          local playerIndex = GetPlayerFromServerId(serverId)
          if playerIndex and playerIndex ~= -1 then
            local targetPed = GetPlayerPed(playerIndex)
            if targetPed and targetPed ~= playerPed then
              local dist = #(myCoords - GetEntityCoords(targetPed))
              if dist <= 3.0 then
                -- Draw prompt above the downed player's head
                local targetPos = GetEntityCoords(targetPed)
                local onScreen, sx, sy = World3dToScreen2d(targetPos.x, targetPos.y, targetPos.z + 1.0)
                if onScreen then
                  SetTextFont(4)
                  SetTextScale(0.35, 0.35)
                  SetTextColour(255, 255, 255, 215)
                  SetTextCentre(true)
                  BeginTextCommandDisplayText("STRING")
                  AddTextComponentSubstringPlayerName("Press [E] to revive")
                  EndTextCommandDisplayText(sx, sy)
                end
                -- Initiate revive when E is pressed
                if IsControlJustPressed(0, 38) then
                  reviving = true
                  local targetServerId = serverId
                  local playerPed = PlayerPedId()
                  -- Play a medic tending animation (CPR) while reviving.
                  TaskStartScenarioInPlace(playerPed, "CODE_HUMAN_MEDIC_TEND_TO_DEAD", 0, true)
                  -- Revive duration (ms)
                  local duration = 5000
                  local startTime = GetGameTimer()
                  local aborted = false
                  while (GetGameTimer() - startTime) < duration do
                    Citizen.Wait(0)
                    -- Draw progress bar above the minimap to indicate revive progress
                    local elapsed = GetGameTimer() - startTime
                    local progress = math.min(1.0, elapsed / duration)
                    DrawRect(0.5, 0.88, 0.22, 0.025, 0, 0, 0, 150)
                    DrawRect(0.39 + progress * 0.22 / 2, 0.88, progress * 0.22, 0.025, 0, 255, 0, 200)
                    -- Cancel if the medic moves too far away
                    if #(GetEntityCoords(playerPed) - GetEntityCoords(targetPed)) > 4.0 then
                      aborted = true
                      break
                    end
                  end
                  -- Stop the revive animation
                  ClearPedTasksImmediately(playerPed)
                  -- If not aborted and still close to the target, trigger server revive
                  if not aborted and #(GetEntityCoords(playerPed) - GetEntityCoords(targetPed)) <= 4.0 then
                    TriggerServerEvent('koth:revivePlayer', targetServerId)
                  end
                  reviving = false
                end
                break
              end
            end
          end
        end
      end
    end
  end
end)

--[[
  Spawn the Prestige Shop NPC at the provided coordinates.  The server
  triggers this event after each map rotation, passing a table with x,
  y and z fields.  We spawn a static ped using a generic model,
  freeze it in place and store it in prestigePed so we can delete it
  when the map rotates again.  While the player is near the ped they
  can press E to open the prestige menu or attempt to prestige.  The
  actual prestige logic is handled server‑side.  When the ped is
  removed (for example on new map), we clean up the entity.
]]
RegisterNetEvent('koth:spawnPrestigePed')
AddEventHandler('koth:spawnPrestigePed', function(coords)
  -- Clean up any existing ped
  if prestigePed and DoesEntityExist(prestigePed) then
    DeletePed(prestigePed)
    prestigePed = nil
  end
  if not coords or not coords.x then return end
  -- Load a ped model that is allowed by the ped blocker.  We reuse
  -- the same model used for the existing shop peds (autoshop worker)
  -- so that the prestige NPC spawns correctly even when a ped blocker
  -- script limits ped models to a specific set.  If you change the
  -- allowed models in the ped blocker, update this hash accordingly.
  local modelHash = GetHashKey('s_m_m_autoshop_02')
  RequestModel(modelHash)
  while not HasModelLoaded(modelHash) do Wait(10) end
  prestigePed = CreatePed(4, modelHash, coords.x, coords.y, coords.z, coords.heading or 0.0, false, true)
  SetEntityAsMissionEntity(prestigePed, true, true)
  SetBlockingOfNonTemporaryEvents(prestigePed, true)
  SetEntityInvincible(prestigePed, true)
  FreezeEntityPosition(prestigePed, true)
  -- Thread to draw a prompt and handle interaction
  Citizen.CreateThread(function()
    while prestigePed and DoesEntityExist(prestigePed) do
      local playerPed = PlayerPedId()
      local dist = #(GetEntityCoords(playerPed) - GetEntityCoords(prestigePed))
      if dist < 3.0 then
        -- Draw 3D text prompt above ped
        DrawText3D(coords.x, coords.y, coords.z + 1.2, "Press E to open Prestige Shop", { r = 255, g = 215, b = 0 })
        -- If player presses E, open the prestige shop rather than directly prestiging.
        if IsControlJustReleased(0, 38) then -- 38 is INPUT_PICKUP (E)
          -- Ask the server for our prestige data.  The server will respond
          -- with a koth:receivePrestigeData event containing our current
          -- prestige rank and token counts.  Once received, the client
          -- will open the prestige shop UI.  Do not immediately call
          -- koth:prestige here; instead allow the player to confirm.
          TriggerServerEvent('koth:getPrestigeData')
          -- Small delay to prevent repeated triggering if the key is held
          Wait(1000)
        end
      end
      Citizen.Wait(0)
    end
  end)
end)

-- Register a callback for clickable map voting from the NUI.  When a
-- player clicks a map option in the HTML UI, the browser sends
-- a `voteMap` callback with the selected index.  Forward this
-- index to the server and acknowledge the callback.
RegisterNUICallback('voteMap', function(data, cb)
  local idx = data and data.index
  if idx then
    TriggerServerEvent('koth:submitVote', idx)
  end
  if cb then cb({}) end
end)

--[[
  -----------------------------------------------------------------------
  KOTH Round Leaderboard & Map Controls

  This block of code implements a simple leaderboard that appears when
  the player holds the TAB key.  The leaderboard shows the top 15
  players for the current KOTH round based on their kill count.  It
  leverages the server‑side `roundKills` table, which is reset each
  time the map rotates.  When TAB is pressed, a request is sent to
  the server for the latest leaderboard data.  The response is
  consumed via the `koth:receiveLeaderboard` event and stored
  client‑side.  A drawing thread renders the leaderboard as an
  overlay on the screen while the key is held.  Colours are assigned
  based on team to aid quick recognition.

  Additionally, this section binds the ESC key to toggle the large
  minimap.  When ESC is pressed the radar is expanded; releasing
  the key returns the radar to its normal size.  This offers a
  convenient way for players to quickly view the map without
  entering the pause menu.
  -----------------------------------------------------------------------
]]

-- Whether the leaderboard is currently visible
local leaderboardVisible = false
-- Holds the leaderboard data received from the server.  Each entry
-- should be a table with `name`, `kills` and `team` fields.
local leaderboardData = {}

-- Variables for round timer.  The server sends the start time and
-- duration of each round via the 'koth:roundStarted' event.  These
-- values are used to compute the remaining time displayed on the
-- leaderboard.
local roundStartTimeClient = nil
local roundDurationClient = nil

-- Holds the mapping from map index to friendly name for the current vote.
-- When a vote starts, this table is populated with entries like
-- { [5] = "Church", [9] = "Construction", ... }.  It is used when
-- displaying the result of the vote so that the winning map name can
-- be shown even after the vote UI has been hidden.  The table is
-- cleared after each vote.
local currentVoteOptions = {}

-- Handle round start notifications from the server.  When a new round
-- begins, the server broadcasts the start time and duration to all
-- clients.  Store these values locally for the timer display.
RegisterNetEvent('koth:roundStarted')
AddEventHandler('koth:roundStarted', function(info)
  if info then
    roundStartTimeClient = info.startTime
    roundDurationClient = info.duration
  else
    roundStartTimeClient = nil
    roundDurationClient = nil
  end
end)

-- ---------------------------------------------------------------------------
-- Entity enumeration helpers
--
-- Some FiveM environments do not support GetGamePool.  To reliably
-- disable collisions for ghost mode we provide robust enumeration
-- functions that fall back to FindFirst/FindNext pairs when needed.

--- Generic enumerator.  Yields entity handles via a coroutine until
--- enumeration ends.  Accepts initialiser, iterator and disposer functions.
local function EnumerateEntities(initFunc, moveFunc, disposeFunc)
  return coroutine.wrap(function()
    local iter, id = initFunc()
    if not id or id == 0 then
      disposeFunc(iter)
      return
    end
    repeat
      coroutine.yield(id)
      local next, nextId = moveFunc(iter)
      id = nextId
    until not next
    disposeFunc(iter)
  end)
end

--- Iterate over all vehicles in the world.  Uses GetGamePool if available
--- otherwise falls back to the generic enumerator.
local function EnumerateVehicles()
  if GetGamePool then
    local pool = GetGamePool('CVehicle')
    if pool then
      return coroutine.wrap(function()
        for _, veh in ipairs(pool) do
          coroutine.yield(veh)
        end
      end)
    end
  end
  return EnumerateEntities(FindFirstVehicle, FindNextVehicle, EndFindVehicle)
end

--- Iterate over all peds in the world.  Uses GetGamePool if available
--- otherwise falls back to the generic enumerator.
local function EnumeratePeds()
  if GetGamePool then
    local pool = GetGamePool('CPed')
    if pool then
      return coroutine.wrap(function()
        for _, ped in ipairs(pool) do
          coroutine.yield(ped)
        end
      end)
    end
  end
  return EnumerateEntities(FindFirstPed, FindNextPed, EndFindPed)
end

-- Receive leaderboard data from the server and update the local
-- cache.  This event is triggered by the server in response to
-- `koth:requestLeaderboard`.
RegisterNetEvent('koth:receiveLeaderboard')
AddEventHandler('koth:receiveLeaderboard', function(data)
  leaderboardData = data or {}
end)

-- Drawing thread for the leaderboard.  When the leaderboard is
-- visible it draws a semi‑transparent background, a header and up to
-- 15 rows of player names and kill counts.  Colours are derived
-- from team names.  The thread runs continuously but does very
-- little work when the leaderboard is hidden.
Citizen.CreateThread(function()
  --
  -- Leaderboard drawing loop
  --
  -- This loop runs every frame and draws the leaderboard when
  -- `leaderboardVisible` is true.  The board is designed to be
  -- aesthetically pleasing and easy to read.  It is centred
  -- horizontally and vertically on the player's screen.  The layout
  -- uses separate columns for the player name and kill count, with
  -- proportional sizing to accommodate long names while keeping
  -- numbers aligned to the right.  Top three positions are
  -- highlighted with medal colours and rows alternate shading for
  -- readability.
  while true do
    Citizen.Wait(0)
    if leaderboardVisible then
      -- Configure how many leaderboard entries to display
      local maxEntries = 15
      local numEntries = math.min(#leaderboardData, maxEntries)
      -- Board dimensions: reduce width slightly for a cleaner look
      local width = 0.40
      -- Allocate a small portion of the width to the kills column.  A
      -- lower fraction keeps numbers aligned to the right without
      -- crowding the names.
      local killsFraction = 0.15
      local killsColWidth = width * killsFraction
      local nameColWidth  = width - killsColWidth
      -- Section heights
      local headerHeight  = 0.05
      local rowHeight     = 0.03
      -- Top and bottom padding
      local verticalMargin = 0.02
      -- Compute total height: margins + header + heading row + entry rows
      local totalHeight = verticalMargin * 2 + headerHeight + rowHeight + (numEntries * rowHeight)
      -- Centre the leaderboard on screen
      local x = 0.5 - (width / 2.0)
      local y = 0.5 - (totalHeight / 2.0)
      -- Draw the semi‑transparent backing rectangle
      DrawRect(x + width / 2.0, y + totalHeight / 2.0, width, totalHeight, 0, 0, 0, 150)
      -- Draw the header bar
      local headerY = y + verticalMargin + headerHeight / 2.0
      DrawRect(x + width / 2.0, headerY, width, headerHeight, 30, 30, 30, 220)
      -- Draw title
      SetTextFont(0)
      SetTextScale(0.40, 0.40)
      SetTextColour(255, 255, 255, 255)
      SetTextCentre(true)
      BeginTextCommandDisplayText("STRING")
      AddTextComponentSubstringPlayerName("KOTH ROUND KILLS")
      EndTextCommandDisplayText(x + width / 2.0, headerY - headerHeight / 2.6)
      -- Draw round timer if available.  The timer counts down from the
      -- configured round duration to zero.  It is rendered at the right
      -- side of the header bar.  When the timer reaches 00:00 it will
      -- display as 00:00 until the next round begins.
      if roundStartTimeClient and roundDurationClient then
        local elapsed = GetGameTimer() - roundStartTimeClient
        local remaining = roundDurationClient - elapsed
        if remaining < 0 then remaining = 0 end
        local minutes = math.floor(remaining / 60000)
        local seconds = math.floor((remaining % 60000) / 1000)
        local timerText = string.format("%02d:%02d", minutes, seconds)
        SetTextFont(0)
        SetTextScale(0.32, 0.32)
        SetTextColour(200, 200, 200, 255)
        SetTextCentre(false)
        BeginTextCommandDisplayText("STRING")
        AddTextComponentSubstringPlayerName(timerText)
        -- Position timer near the right edge of the header; subtract a
        -- small margin.  The y coordinate matches the title's baseline.
        EndTextCommandDisplayText(x + width - 0.015, headerY - headerHeight / 2.6)
      end
      -- Start y position for rows after header
      local currentY = y + verticalMargin + headerHeight
      -- Draw heading row background
      local headingCenterY = currentY + rowHeight / 2.0
      DrawRect(x + width / 2.0, headingCenterY, width, rowHeight, 50, 50, 50, 210)
      -- Column heading: Rank & Name
      SetTextFont(0)
      SetTextScale(0.30, 0.30)
      SetTextColour(220, 220, 220, 255)
      SetTextCentre(false)
      BeginTextCommandDisplayText("STRING")
      AddTextComponentSubstringPlayerName("Rank & Name")
      EndTextCommandDisplayText(x + 0.015, headingCenterY - rowHeight / 2.7)
      -- Column heading: Kills
      SetTextFont(0)
      SetTextScale(0.30, 0.30)
      SetTextCentre(true)
      SetTextColour(220, 220, 220, 255)
      BeginTextCommandDisplayText("STRING")
      AddTextComponentSubstringPlayerName("Kills")
      EndTextCommandDisplayText(x + nameColWidth + killsColWidth / 2.0, headingCenterY - rowHeight / 2.7)
      -- Move currentY past the heading row
      currentY = currentY + rowHeight
      -- Draw entry rows
      for idx, entry in ipairs(leaderboardData) do
        if idx > maxEntries then break end
        local rowCenterY = currentY + rowHeight / 2.0
        -- Determine background colour: medal colours for top 3, otherwise alternating dark shades
        local bgR, bgG, bgB, bgA
        if idx == 1 then
          bgR, bgG, bgB, bgA = 255, 215, 0, 120
        elseif idx == 2 then
          bgR, bgG, bgB, bgA = 192, 192, 192, 120
        elseif idx == 3 then
          bgR, bgG, bgB, bgA = 205, 127, 50, 120
        elseif (idx % 2) == 0 then
          bgR, bgG, bgB, bgA = 20, 20, 20, 110
        else
          bgR, bgG, bgB, bgA = 30, 30, 30, 110
        end
        DrawRect(x + width / 2.0, rowCenterY, width, rowHeight, bgR, bgG, bgB, bgA)
        -- Determine text colour based on team
        local tr, tg, tb = 255, 255, 255
        if entry.team == 'red' then
          tr, tg, tb = 255, 80, 80
        elseif entry.team == 'blue' then
          tr, tg, tb = 80, 160, 255
        elseif entry.team == 'green' then
          tr, tg, tb = 80, 255, 80
        end
        -- Draw rank and name
        SetTextFont(0)
        SetTextScale(0.28, 0.28)
        SetTextColour(tr, tg, tb, 255)
        SetTextCentre(false)
        BeginTextCommandDisplayText("STRING")
        AddTextComponentSubstringPlayerName(string.format("%2d. %s", idx, entry.name or "Unknown"))
        EndTextCommandDisplayText(x + 0.015, rowCenterY - rowHeight / 2.8)
        -- Draw kills count
        SetTextFont(0)
        SetTextScale(0.28, 0.28)
        SetTextColour(235, 235, 235, 255)
        SetTextCentre(true)
        BeginTextCommandDisplayText("STRING")
        AddTextComponentSubstringPlayerName(tostring(entry.kills or 0))
        EndTextCommandDisplayText(x + nameColWidth + killsColWidth / 2.0, rowCenterY - rowHeight / 2.8)
        -- Advance to next row
        currentY = currentY + rowHeight
      end
    end
  end
end)

-- Command to show the leaderboard when the key is pressed.  Using
-- paired plus/minus commands allows the leaderboard to remain
-- visible while the key is held and automatically hide when it is
-- released.  On key down we request fresh data from the server so
-- that the leaderboard reflects any recent kills.
RegisterCommand('+kothLeaderboard', function()
  leaderboardVisible = true
  TriggerServerEvent('koth:requestLeaderboard')
end, false)

RegisterCommand('-kothLeaderboard', function()
  leaderboardVisible = false
end, false)

-- Bind the TAB key to the leaderboard commands.  Players can
-- customise this binding in their key settings if desired.  The
-- second argument is a description shown in the FiveM settings menu.
RegisterKeyMapping('+kothLeaderboard', 'Show KOTH round leaderboard', 'keyboard', 'TAB')

-- Toggle the big map using ESC.  The map is expanded when the key
-- is pressed and returns to normal size when released.  The second
-- parameter of SetRadarBigmapEnabled determines whether the full
-- screen map is shown; passing false keeps it to the larger minimap.
RegisterCommand('+kothMap', function()
  SetRadarBigmapEnabled(true, false)
end, false)

RegisterCommand('-kothMap', function()
  SetRadarBigmapEnabled(false, false)
end, false)

RegisterKeyMapping('+kothMap', 'Open the map', 'keyboard', 'ESC')

--
-- AUTO DELETE PLAYER VEHICLES
--
-- Some servers implement an "auto dv" system that removes player‑spawned
-- vehicles after a set period of inactivity.  To fulfil the user's
-- request to reduce this timeout from 2 minutes 30 seconds to just
-- 30 seconds, we add a client‑side watcher that tracks the last
-- player‑owned vehicle and deletes it if the player has been
-- outside of it for 30 seconds.  Only vehicles spawned via the
-- KOTH system (marked with the `PlayerVehicle` decorator) are
-- affected.  This helps keep spawn areas clear without abruptly
-- removing other cars players might be using.
Citizen.CreateThread(function()
  local trackedVeh = nil
  local exitTimestamp = nil
  while true do
    Citizen.Wait(1000)
    local ped = PlayerPedId()
    -- If the player is inside a vehicle, update the tracked vehicle and reset timer
    if IsPedInAnyVehicle(ped, false) then
      local veh = GetVehiclePedIsIn(ped, false)
      -- Only track vehicles that are player‑owned via decorator
      if DoesEntityExist(veh) and DecorExistOn(veh, "PlayerVehicle") and DecorGetBool(veh, "PlayerVehicle") then
        -- If we've switched vehicles, update the reference
        if veh ~= trackedVeh then
          trackedVeh = veh
        end
        exitTimestamp = nil
      else
        -- If current vehicle isn't player owned, don't track it
        trackedVeh = nil
        exitTimestamp = nil
      end
    else
      -- Not in any vehicle; check if we had a tracked vehicle and if it still exists
      if trackedVeh and DoesEntityExist(trackedVeh) then
        if not exitTimestamp then
          -- Record the time when the player exited the vehicle
          exitTimestamp = GetGameTimer()
        elseif (GetGameTimer() - exitTimestamp) >= 30000 then
          -- 30 seconds have passed outside the vehicle; delete it
          DeleteEntity(trackedVeh)
          trackedVeh = nil
          exitTimestamp = nil
        end
      else
        -- Vehicle no longer exists, reset tracking
        trackedVeh = nil
        exitTimestamp = nil
      end
    end
  end
end)

-- Request team counts and show UI
AddEventHandler('playerSpawned', function()
  print('[KOTH] Player spawned event triggered')
  print('[KOTH] hasSelectedTeam:', hasSelectedTeam)
  print('[KOTH] playerTeam:', playerTeam)

  -- Only show team selection if not already selected
  if not hasSelectedTeam and not playerTeam and not pendingTeamSelection then
    Citizen.SetTimeout(100, function()
      print('[KOTH] Showing team selection UI...')
      TriggerServerEvent('koth:requestCounts')

      -- Freeze player until they select a team
      local playerPed = PlayerPedId()
      FreezeEntityPosition(playerPed, true)
      SetEntityInvincible(playerPed, true)
    end)
  else
    print('[KOTH] Player already has team, skipping team selection UI')
    -- Make sure player is unfrozen if they already have a team
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, false)
    SetEntityInvincible(playerPed, false)
  end

  -- Always request player data
  TriggerServerEvent('koth:requestPlayerData')
end)

-- Also trigger on resource start for testing
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  -- Don't load stored team on resource start - always show team selection
  print('[KOTH] Resource started - clearing any stored team for fresh selection')
  playerTeam = nil
  hasSelectedTeam = false
  DeleteResourceKvp('playerTeam')

  -- Initialize HUD with default values to prevent showing full XP bar
  SendNUIMessage({
    action = 'initializeHUD',
    playerData = {
      money = 0,
      xp = 0,
      level = 1,
      kills = 0,
      deaths = 0,
      zone_kills = 0,
      player_name = GetPlayerName(PlayerId())
    }
  })

  -- Notify server that client is ready
  TriggerServerEvent('koth:clientReady')

  -- Wait a bit for player to fully load
  Citizen.SetTimeout(2000, function()
    print('[KOTH] Resource started, requesting team counts...')
    TriggerServerEvent('koth:requestCounts')

    -- Freeze player until they select a team
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, true)
    SetEntityInvincible(playerPed, true)

    -- Always request player data
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

--[[
  The following command was previously used for development to reset the
  locally stored team selection.  It has been disabled to prevent
  ordinary players from seeing or using it.
]]
-- RegisterCommand('resetteamselect', function()
--   print('[KOTH] Resetting stored team and selection state')
--   playerTeam = nil
--   hasSelectedTeam = false
--   DeleteResourceKvp('playerTeam')
--   print('[KOTH] Stored team cleared, you can now select a team again')
-- end, false)

-- Handle player data updates from server (DATABASE SYNCED)
RegisterNetEvent('koth:updatePlayerData', function(data)
  print('[KOTH] Received player data update from server:', json.encode(data))
  playerStats = data

  -- Update HUD with real player data from database
  if playerStats then
    print('[KOTH] Updating HUD with player data - Money: $' .. (playerStats.money or 0) .. ', XP: ' .. (playerStats.xp or 0))

    SendNUIMessage({
      action = 'updatePlayerData',
      data = {
        player_name = playerStats.player_name or GetPlayerName(PlayerId()),
        money = playerStats.money or 0,
        level = playerStats.level or 1,
        xp = playerStats.xp or 0,
        kills = playerStats.kills or 0,
        deaths = playerStats.deaths or 0,
        zone_kills = playerStats.zone_kills or 0,
        total_playtime = playerStats.total_playtime or 0,
        -- Include prestige details so the UI can display them on the HUD
        prestigeRank = playerStats.prestigeRank or 0,
        prestigeWeaponTokens = playerStats.prestigeWeaponTokens or 0,
        prestigeVehicleTokens = playerStats.prestigeVehicleTokens or 0,
        -- Forward VIP status so HUD can toggle 1.5x banner immediately
        isVip = playerStats.isVip or false
      }
    })

    print('[KOTH] HUD updated successfully - Money: $' .. (playerStats.money or 0) .. ', XP: ' .. (playerStats.xp or 0))
  else
    print('[KOTH] ERROR: Received null player data from server')
  end
end)

-- Handle kill reward event to show UI and update HUD
RegisterNetEvent('koth:showKillReward', function(data)
  print('=== [KOTH CLIENT] KILL REWARD EVENT RECEIVED ===')
  print('[KOTH] Raw kill reward data:', json.encode(data))

  if not data then
    print('[KOTH] ERROR: Kill reward data is nil!')
    return
  end

  print('[KOTH] Kill reward details - XP: ' .. tostring(data.xp) .. ', Money: $' .. tostring(data.money) .. ', Zone: ' .. tostring(data.inZone))

  -- Send to NUI to show kill reward popup
  local nuiData = {
    action = 'showKillReward',
    xp = data.xp,
    money = data.money,
    inZone = data.inZone,
    victim = data.victimName,
    isVip = data.isVip,
    multiplier = data.multiplier
  }

  print('[KOTH] Sending to NUI:', json.encode(nuiData))
  SendNUIMessage(nuiData)

  -- Update player money and XP in HUD
  if playerStats then
    local oldMoney = playerStats.money or 0
    local oldXP = playerStats.xp or 0

    playerStats.money = oldMoney + (data.money or 0)
    playerStats.xp = oldXP + (data.xp or 0)

    -- Recalculate level based on new XP
    local function CalculateLevel(xp)
      local levels = {
        {level = 1, required = 0},
        {level = 2, required = 100},
        {level = 3, required = 250},
        {level = 4, required = 500},
        {level = 5, required = 1000},
        {level = 6, required = 1750},
        {level = 7, required = 2750},
        {level = 8, required = 4000},
        {level = 9, required = 6000},
        {level = 10, required = 8500}
      }
      local currentLevel = 1
      for _, levelData in ipairs(levels) do
        if xp >= levelData.required then
          currentLevel = levelData.level
        else
          break
        end
      end
      return currentLevel
    end

    local newLevel = CalculateLevel(playerStats.xp)
    if newLevel ~= playerStats.level then
      print('[KOTH] Player level updated from ' .. tostring(playerStats.level) .. ' to ' .. tostring(newLevel))
      playerStats.level = newLevel
    end

    print('[KOTH] Updated local playerStats - Money: $' .. oldMoney .. ' -> $' .. playerStats.money .. ', XP: ' .. oldXP .. ' -> ' .. playerStats.xp .. ', Level: ' .. playerStats.level)

    local hudUpdateData = {
      action = 'updatePlayerData',
      data = {
        money = playerStats.money,
        xp = playerStats.xp,
        level = playerStats.level
      }
    }

    print('[KOTH] Sending HUD update:', json.encode(hudUpdateData))
    SendNUIMessage(hudUpdateData)
  else
    print('[KOTH] WARNING: playerStats is nil, cannot update local HUD')
  end

  print('[KOTH] Kill reward processing complete')
  print('=== [KOTH CLIENT] KILL REWARD EVENT END ===')
end)

-- TEAM-BASED CLOTHING CONFIGURATION
local teamClothing = {
  red = {
    -- MILITIA VEST - RED
    [9] = { drawable = 62, texture = 0 },  -- Body Armor (vest)
    -- CARGO SHORTS - RED
    [4] = { drawable = 203, texture = 0 }, -- Legs (shorts)
    -- Top - RED
    [11] = { drawable = 544, texture = 0 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  },
  green = {
    -- MILITIA VEST - GREEN
    [9] = { drawable = 62, texture = 1 },  -- Body Armor (vest)
    -- CARGO SHORTS - GREEN
    [4] = { drawable = 203, texture = 2 }, -- Legs (shorts)
    -- Top - GREEN
    [11] = { drawable = 544, texture = 1 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  },
  blue = {
    -- MILITIA VEST - BLUE
    [9] = { drawable = 62, texture = 2 },  -- Body Armor (vest)
    -- CARGO SHORTS - BLUE
    [4] = { drawable = 203, texture = 1 }, -- Legs (shorts)
    -- Top - BLUE
    [11] = { drawable = 544, texture = 2 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  }
}

-- Function to apply team appearance
local function ApplyTeamAppearance(team)
  local playerPed = PlayerPedId()

  -- Change to the specific ped model
  local model = `mp_m_freemode_01`

  if not HasModelLoaded(model) then
    RequestModel(model)
    while not HasModelLoaded(model) do
      Citizen.Wait(0)
    end
  end

  -- Change the player model
  SetPlayerModel(PlayerId(), model)
  playerPed = PlayerPedId() -- Get new ped after model change

  -- Apply team-specific clothing
  if teamClothing[team] then
    for componentId, clothing in pairs(teamClothing[team]) do
      SetPedComponentVariation(playerPed, componentId, clothing.drawable, clothing.texture, 0)
    end

    -- Set face and hair (consistent across all teams)
    SetPedComponentVariation(playerPed, 0, 0, 0, 0)  -- Face
    SetPedComponentVariation(playerPed, 2, 11, 4, 0) -- Hair (buzz cut style)
    SetPedHairColor(playerPed, 0, 0) -- Black hair

    -- Remove any props (hats, glasses, etc.)
    ClearPedProp(playerPed, 0) -- Hat
    ClearPedProp(playerPed, 1) -- Glasses
    ClearPedProp(playerPed, 2) -- Ear
    ClearPedProp(playerPed, 6) -- Watch
    ClearPedProp(playerPed, 7) -- Bracelet

    print(('[KOTH] Applied %s team appearance'):format(team))
  else
    print(('[KOTH] No clothing config found for team: %s'):format(team))
  end

  -- Ensure the model doesn't get reset
  SetModelAsNoLongerNeeded(model)
end

-- =======================================================================
-- Class Clothing System
-- =======================================================================
--
-- In addition to team colours, this resource now supports class‑specific
-- outfits.  Each class maps to a set of ped component variations based on
-- the jqsus_vantage clothing pack.  Because the actual clothes_dump.json
-- file is not packaged with this resource, the class outfits below reuse
-- the same component IDs from the team clothing definitions.  Server
-- owners can customise these values to match their own clothing pack.

-- Define a clothing set for each class and team colour.  Each entry
-- contains a `components` table keyed by component ID and a `props`
-- table keyed by prop index (0 = hat, 1 = glasses, 2 = ears).  The
-- values below are derived from the supplied clothes_dump.json file.  If
-- you wish to tweak colours or use additional drawables, modify this
-- table accordingly.
local classClothing = {
  assault = {
    red = {
      components = {
        [1]  = { drawable = 244, texture = 0 }, -- Black shiesty mask
        [9]  = { drawable = 62,  texture = 0 }, -- Assault tac vest
        [4]  = { drawable = 202, texture = 0 }, -- Cargo pants (red)
        [11] = { drawable = 544, texture = 0 }, -- Top (red)
        [3]  = { drawable = 214, texture = 0 }, -- Gloves
        [6]  = { drawable = 151, texture = 0 }, -- Shoes
        [8]  = { drawable = 213, texture = 0 }, -- Assault undershirt
      },
      props = {
        [0] = { drawable = 221, texture = 0 }, -- Assault helmet
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 }, -- Headset (ears)
      }
    },
    green = {
      components = {
        [1]  = { drawable = 244, texture = 0 }, -- Black shiesty mask
        [9]  = { drawable = 62,  texture = 0 }, -- Vest remains the same
        [4]  = { drawable = 202, texture = 1 }, -- Cargo pants (green)
        [11] = { drawable = 544, texture = 1 }, -- Top (green)
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 213, texture = 0 },
      },
      props = {
        [0] = { drawable = 221, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    blue = {
      components = {
        [1]  = { drawable = 244, texture = 0 }, -- Black shiesty mask
        [9]  = { drawable = 62,  texture = 0 },
        [4]  = { drawable = 202, texture = 2 }, -- Cargo pants (blue)
        [11] = { drawable = 544, texture = 2 }, -- Top (blue)
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 213, texture = 0 },
      },
      props = {
        [0] = { drawable = 221, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    }
  },
  medic = {
    red = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 63, texture = 0 }, -- Medic vest
        [4]  = { drawable = 202, texture = 0 },
        [11] = { drawable = 544, texture = 0 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 214, texture = 0 }, -- Medic holster
      },
      props = {
        [0] = { drawable = 222, texture = 0 }, -- Medic hat
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    green = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 63, texture = 0 },
        [4]  = { drawable = 202, texture = 1 },
        [11] = { drawable = 544, texture = 1 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 214, texture = 0 },
      },
      props = {
        [0] = { drawable = 222, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    blue = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 63, texture = 0 },
        [4]  = { drawable = 202, texture = 2 },
        [11] = { drawable = 544, texture = 2 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 214, texture = 0 },
      },
      props = {
        [0] = { drawable = 222, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    }
  },
  engineer = {
    red = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 64, texture = 0 }, -- Engineer vest
        [4]  = { drawable = 202, texture = 0 },
        [11] = { drawable = 544, texture = 0 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 215, texture = 0 }, -- Engineer holster
      },
      props = {
        [0] = { drawable = 223, texture = 0 }, -- Engineer hat
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    green = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 64, texture = 0 },
        [4]  = { drawable = 202, texture = 1 },
        [11] = { drawable = 544, texture = 1 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 215, texture = 0 },
      },
      props = {
        [0] = { drawable = 223, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    blue = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 64, texture = 0 },
        [4]  = { drawable = 202, texture = 2 },
        [11] = { drawable = 544, texture = 2 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 215, texture = 0 },
      },
      props = {
        [0] = { drawable = 223, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    }
  },
  heavy = {
    red = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 65, texture = 0 }, -- Heavy vest
        [4]  = { drawable = 202, texture = 0 },
        [11] = { drawable = 544, texture = 0 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 219, texture = 0 }, -- Heavy undershirt (variant 0)
      },
      props = {
        [0] = { drawable = 224, texture = 0 }, -- Heavy hat
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    green = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 65, texture = 0 },
        [4]  = { drawable = 202, texture = 1 },
        [11] = { drawable = 544, texture = 1 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 219, texture = 0 },
      },
      props = {
        [0] = { drawable = 224, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    blue = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 65, texture = 0 },
        [4]  = { drawable = 202, texture = 2 },
        [11] = { drawable = 544, texture = 2 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 219, texture = 0 },
      },
      props = {
        [0] = { drawable = 224, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    }
  },
  scout = {
    red = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 66, texture = 0 }, -- Scout vest
        [4]  = { drawable = 202, texture = 0 },
        [11] = { drawable = 544, texture = 0 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 216, texture = 0 }, -- Scout undershirt
      },
      props = {
        [0] = { drawable = 225, texture = 0 }, -- Scout helmet
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    green = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 66, texture = 0 },
        [4]  = { drawable = 202, texture = 1 },
        [11] = { drawable = 544, texture = 1 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 216, texture = 0 },
      },
      props = {
        [0] = { drawable = 225, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    },
    blue = {
      components = {
        [1]  = { drawable = 244, texture = 0 },
        [9]  = { drawable = 66, texture = 0 },
        [4]  = { drawable = 202, texture = 2 },
        [11] = { drawable = 544, texture = 2 },
        [3]  = { drawable = 214, texture = 0 },
        [6]  = { drawable = 151, texture = 0 },
        [8]  = { drawable = 216, texture = 0 },
      },
      props = {
        [0] = { drawable = 225, texture = 0 },
        -- Glasses removed when mask is used
        [2] = { drawable = 42,  texture = 0 },
      }
    }
  }
}

-- Function to apply class appearance
local function ApplyClassAppearance(classId)
  local playerPed = PlayerPedId()
  -- Always use the freemode ped model for consistency
  local model = `mp_m_freemode_01`
  if not HasModelLoaded(model) then
    RequestModel(model)
    while not HasModelLoaded(model) do
      Citizen.Wait(0)
    end
  end
  -- Change the player model to freemode
  SetPlayerModel(PlayerId(), model)
  playerPed = PlayerPedId()

  -- Determine the player's team; default to red if not assigned yet
  local team = playerTeam or 'red'
  local classEntry = classClothing[classId]
  if classEntry then
    local variant = classEntry[team] or classEntry['red']
    if variant and variant.components then
      -- Apply each component variation
      for componentId, clothing in pairs(variant.components) do
        SetPedComponentVariation(playerPed, componentId, clothing.drawable or 0, clothing.texture or 0, 0)
      end
      -- Apply consistent face and make the character bald.  Component 2
      -- with drawable 0 and texture 0 represents a shaved head on the
      -- freemode ped.  We still set the face component to default.
      SetPedComponentVariation(playerPed, 0, 0, 0, 0)  -- Face
      SetPedComponentVariation(playerPed, 2, 0, 0, 0)  -- Hair (bald)
      SetPedHairColor(playerPed, 0, 0)

      -- Clear all props first
      for i = 0, 7 do
        ClearPedProp(playerPed, i)
      end
      -- Apply props (hats, glasses, etc.) if defined
      if variant.props then
        for propIndex, prop in pairs(variant.props) do
          SetPedPropIndex(playerPed, propIndex, prop.drawable or -1, prop.texture or 0, true)
        end
      end
      print(('[KOTH] Applied %s class appearance for team %s'):format(tostring(classId), tostring(team)))
    else
      print(('[KOTH] No team variant found for class %s and team %s'):format(tostring(classId), tostring(team)))
    end
  else
    print(('[KOTH] No clothing config found for class: %s'):format(tostring(classId)))
  end
  SetModelAsNoLongerNeeded(model)
end

-- Handle spawn event from server
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
  print('[KOTH] Spawning player at team location')

  local playerPed = PlayerPedId()

  -- Upon successful spawn, update local team state based on payload.  This ensures
  -- the client only commits to a team after server validation.
  if spawnData and spawnData.team then
    playerTeam = spawnData.team
    hasSelectedTeam = true
    -- Persist selected team for respawn system
    SetResourceKvp('playerTeam', spawnData.team)
    TriggerEvent('koth:teamSelected', spawnData.team)

    -- Clear pending flag now that the selection succeeded
    pendingTeamSelection = false
  end

  -- Apply class appearance BEFORE teleporting.  This overrides any
  -- team outfit and ensures the selected class outfit is used.  If the
  -- player has not selected a class yet, default to the assault outfit.
  -- Use a short timeout to ensure the freemode model has been fully loaded
  -- before applying the outfit; without this delay some components may not stick.
  Citizen.SetTimeout(100, function()
    ApplyClassAppearance(currentClass or 'assault')
  end)
  playerPed = PlayerPedId() -- Get new ped after appearance change

  -- Determine a safe coordinate for the player.  Attempt to use
  -- GetSafeCoordForPed which returns a position clear of obstacles.
  local x, y, z = spawnData.x, spawnData.y, spawnData.z
  local foundSafe, safeX, safeY, safeZ = GetSafeCoordForPed(x, y, z, true, 16)
  if foundSafe then
    -- Use the safe coordinates returned
    SetEntityCoords(playerPed, safeX, safeY, safeZ, false, false, false, true)
  else
    -- Fallback: probe the ground near the intended spawn
    local probeHeight = z + 100.0
    local foundGround, groundZ = GetGroundZFor_3dCoord(x, y, probeHeight, 0)
    local finalZ = z
    if foundGround then
      finalZ = groundZ + 1.0
    end
    SetEntityCoords(playerPed, x, y, finalZ, false, false, false, true)
  end
  -- Set heading separately
  SetEntityHeading(playerPed, spawnData.heading or 0.0)

  -- Ensure player is on ground and unfrozen
  SetEntityCollision(playerPed, true, true)
  FreezeEntityPosition(playerPed, false)
  SetEntityInvincible(playerPed, false)

  -- Disable ragdoll and make the player collision‑proof so they cannot
  -- be griefed by vehicles.  SetEntityProofs prevents collision damage
  -- while preserving world collisions.  We also disable ragdoll to
  -- avoid being knocked down by vehicles.  These settings persist
  -- until the player respawns again.
  SetPedCanRagdoll(playerPed, false)
  -- bulletProof, fireProof, explosionProof, collisionProof,
  -- meleeProof, steamProof, drownProof, bulletShockProof
  SetEntityProofs(playerPed, false, false, false, true, false, false, false, false)

  -- Give basic weapon
  GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)

  -- Initialize HUD
  SendNUIMessage({
    action = 'initHUD',
    playerData = playerStats
  })

  -- Request fresh team counts after spawning
  Citizen.SetTimeout(500, function()
    print('[KOTH] Requesting team counts after spawn...')
    TriggerServerEvent('koth:requestCounts')
  end)

  -- Enable PVP
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)

  -- Make sure player can move
  ClearPedTasksImmediately(playerPed)

  -- Enable PVP for this player.  Friendly fire will be mitigated via
  -- health restoration in the damage event handler, so we keep
  -- global friendly fire enabled.  This allows attacking enemies
  -- normally while the script cancels and restores damage for
  -- teammates.
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)

  print('[KOTH] Player spawned successfully and unfrozen')

  -- Hide team selection UI now that spawning succeeded
  SendNUIMessage({ action = 'hideAll' })
  closeUIFocus()

  -- After initial spawn, prompt daily reward UI
  Citizen.SetTimeout(500, function()
    TriggerServerEvent('koth:requestDailyStatus')
  end)

end)

-- Apply team appearance on respawn
AddEventHandler('playerSpawned', function()
  -- Wait a moment for spawn to complete
  Citizen.SetTimeout(1000, function()
    if hasSelectedTeam then
      -- Reapply the previously selected class outfit after respawn
      print('[KOTH] Reapplying class appearance after respawn')
      ApplyClassAppearance(currentClass or 'assault')
    end
  end)
end)

--[[
  Development command to manually reapply team outfits.  Disabled for
  production to avoid exposing unnecessary commands to players.
]]
-- RegisterCommand('teamoutfit', function()
--   if playerTeam then
--     -- Reapply the current class outfit instead of the team outfit
--     ApplyClassAppearance(currentClass or 'assault')
--     print('[KOTH] Class appearance reapplied via command')
--   else
--     print('[KOTH] No team selected yet')
--   end
-- end, false)

-- Event handler for applying team appearance (used by death system)
RegisterNetEvent('koth:applyTeamAppearance', function(team)
  if team then
    -- Apply the base team appearance (colour variations etc.)
    ApplyTeamAppearance(team)
    print('[KOTH] Team appearance applied via event for team:', team)
    -- Override with the player's selected class outfit after a short
    -- delay.  The delay ensures the ped change from the death system
    -- completes before applying the custom outfit.
    Citizen.SetTimeout(100, function()
      ApplyClassAppearance(currentClass or 'assault')
    end)
  end
end)

-- Handle class selection events and apply the corresponding outfit.  The
-- koth_classes resource emits this event after the player selects a
-- class from the UI.  We update our currentClass variable so respawns
-- reuse the same outfit.
RegisterNetEvent('koth:classSelected')
AddEventHandler('koth:classSelected', function(classId)
  currentClass = tostring(classId or 'assault')
  print(('[KOTH] Class selected: %s'):format(currentClass))
  ApplyClassAppearance(currentClass)
end)

-- Receive counts and show team select
RegisterNetEvent('koth:updateCounts', function(counts)
  print('[KOTH] Received counts')
  print('[KOTH] Counts data:', json.encode(counts or {}))
  print('[KOTH] hasSelectedTeam:', hasSelectedTeam, 'playerTeam:', playerTeam)

  -- Only show team selection UI if player hasn't selected a team yet
  if not hasSelectedTeam then
    -- Force show the UI even if counts is nil
    SendNUIMessage({
      action = 'showTeamSelect',
      counts = counts or { red = 0, blue = 0, green = 0 }
    })
    openUIFocus()

    print('[KOTH] Team selection UI shown')
  else
    print('[KOTH] Skipping team selection UI - player already has team')
    -- Make sure UI is hidden
    SendNUIMessage({ action = 'hideAll' })
    closeUIFocus()
  end

  -- Always initialize HUD
  SendNUIMessage({ action = 'initHUD' })

  print('[KOTH] NUI message sent')
end)

-- Team selection callback
RegisterNUICallback('selectTeam', function(data, cb)
  print('[KOTH] Team selected:', data.team or 'none')
  if data and data.team then
    -- Do not update local state yet.  Send the desired team to the server
    -- and wait for the spawn event or denial event before committing.
    TriggerServerEvent('koth:pickTeam', data.team)
    print('[KOTH] Team selection sent to server (awaiting validation)')

    -- Set pending flag so that playerSpawned does not redisplay team UI
    pendingTeamSelection = true
  end
  cb('ok')
end)

-- Handle selection denial from server.  The server will invoke this
-- event when team balancing rules prevent joining the requested team.
-- We keep the team selection UI open and do not update local state.
RegisterNetEvent('koth:teamSelectionDenied', function(recommendedTeam)
  print('[KOTH] Server denied team selection.  Recommended team:', tostring(recommendedTeam))
  -- Optionally display a message in the chat or UI
  local msg = 'Team selection denied. Please join the ' .. tostring(recommendedTeam) .. ' team.'
  TriggerEvent('chat:addMessage', { color = {255, 0, 0}, multiline = false, args = {'[KOTH]', msg} })
  -- Ensure the player remains frozen and invincible until they select again
  local ped = PlayerPedId()
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)

  -- Clear pending flag since server responded
  pendingTeamSelection = false
end)

local ownedVehicles = {}

-- Receive updated owned vehicles list from server
RegisterNetEvent('koth:updateOwnedVehicles', function(data)
  print('[KOTH] Received owned vehicles list from server')
  ownedVehicles = {}
  if data and type(data) == 'table' then
    for _, vehicleName in ipairs(data) do
      ownedVehicles[vehicleName] = true
    end
  end
  print('[KOTH] Owned vehicles:', json.encode(ownedVehicles))
end)

-- Receive fresh vehicle shop data with money
RegisterNetEvent('koth:showVehicleShopWithMoney', function(data)
  print('[KOTH] Received vehicle shop data with money:', data.money)

  -- The server may send vehicles under `items` or `vehicles`.  Normalise
  -- into a local list for ownership decoration.
  local list = nil
  if data.items and type(data.items) == 'table' then
    list = data.items
  elseif data.vehicles and type(data.vehicles) == 'table' then
    list = data.vehicles
  end

  -- Mark vehicles as owned if in ownedVehicles table.  This ensures
  -- spawning for free when re-opening the shop.
  if list then
    for _, vehicle in ipairs(list) do
      if ownedVehicles[vehicle.name] then
        vehicle.owned = true
      else
        vehicle.owned = vehicle.owned or false
      end
    end
  end

  SendNUIMessage({
    action = 'showMenu',
    type = 'vehicles',
    items = list,
    money = data.money,
    isVip = data.isVip or false,
    playerLevel = data.playerLevel or data.level or 1
  })
  openUIFocus()
end)

-- Receive fresh class shop data with money and level
RegisterNetEvent('koth:showClassShopWithData', function(data)
  print('[KOTH] Received class shop data with money:', data.money, 'level:', data.level)

  -- Add locked status based on player level
  for i, class in ipairs(data.classes) do
    class.locked = data.level < class.requiredLevel
  end

  SendNUIMessage({
    action = 'showMenu',
    type = 'classes',
    items = data.classes,
    playerLevel = data.level,
    money = data.money
  })
  openUIFocus()
end)

-- Receive fresh weapon shop data with money
RegisterNetEvent('koth:showWeaponShopWithMoney', function(data)
  print('[KOTH] Received weapon shop data with money:', data.money)
  weaponShopOpened = true

  -- Send player class level to the UI for gating and store it locally
  -- Forward class XP data to the UI so it can render the class XP progress
  -- bar in the weapon shop.  If these fields are nil on the server, default
  -- them to zero so the bar displays as empty.
  SendNUIMessage({
    action = 'showWeaponSelect',
    class = data.class,
    weapons = data.weapons,
    money = data.money,
    classLevel = data.classLevel or 0,
    classXP = data.classXP or 0,
    classMaxXP = data.classMaxXP or 0,
    -- Pass through VIP status so the UI can display VIP-only items
    isVip = data.isVip or false
  })
end)

-- Vehicle menu - triggered by ped interaction
RegisterNetEvent('koth:openVehicleMenu', function(items)
  print('[KOTH] Opening vehicle menu')

  -- Define the vehicles available in the shop.  Vehicles are grouped by
  -- category to allow the UI to render section headers (e.g. Transport,
  -- Level 5, Support Level 10).  The first price is the rent cost and
  -- the second price is the full purchase cost, following the format
  -- provided by the user.  VIP vehicles are flagged with vipOnly=true
  -- and require a VIP rank/role to purchase; the UI can hide these if
  -- the player is not VIP.
  local vehicles = items or {
    -- Transport vehicles
    { name = 'Crusader',          rent = 0,   cost = 0,      category = 'Transport',        requiredLevel = 0, img = 'images/vehicles/crusader.png' },
    { name = 'BF400',             rent = 150, cost = 7500,   category = 'Transport',        requiredLevel = 0, img = 'images/vehicles/bf400.png' },
    { name = 'Manchez',           rent = 100, cost = 5000,   category = 'Transport',        requiredLevel = 0, img = 'images/vehicles/manchez.png' },
    { name = 'Baller5',           rent = 2000, cost = 40000,  category = 'Transport',        requiredLevel = 0, img = 'images/vehicles/baller5.png' },
    -- Level 5 vehicles
    { name = 'Kuruma',            rent = 350, cost = 10000,  category = 'Level 5',          requiredLevel = 5, img = 'images/vehicles/kuruma.png' },
    { name = 'Outlaw',            rent = 300, cost = 8000,   category = 'Level 5',          requiredLevel = 5, img = 'images/vehicles/outlaw.png' },
    { name = 'Kamacho',           rent = 300, cost = 12000,  category = 'Level 5',          requiredLevel = 5, img = 'images/vehicles/kamacho.png' },
    -- Level 7 vehicles
    { name = 'Havok',             rent = 400, cost = 18000,  category = 'Level 7',          requiredLevel = 7, img = 'images/vehicles/havok.png' },
    -- Level 10 vehicles
    { name = 'Calico',            rent = 400, cost = 17000,  category = 'Level 10',         requiredLevel = 10, img = 'images/vehicles/calico.png' },
    { name = 'Flash GT',          rent = 350, cost = 13500,  category = 'Level 10',         requiredLevel = 10, img = 'images/vehicles/flashgt.png' },
    { name = 'I-Wagen',           rent = 375, cost = 12500,  category = 'Level 10',         requiredLevel = 10, img = 'images/vehicles/iwagen.png' },
    { name = 'Rumpo',             rent = 500, cost = 25000,  category = 'Level 10',         requiredLevel = 10, img = 'images/vehicles/rumpo.png' },
    -- Level 15 vehicles
    { name = 'Astron',            rent = 400, cost = 22500,  category = 'Level 15',         requiredLevel = 15, img = 'images/vehicles/astron.png' },
    -- Support Level 10 vehicles
    { name = 'Dune FAV',          rent = 400, cost = 20000,  category = 'Support Level 10', requiredLevel = 10, img = 'images/vehicles/dune3.png' },
    { name = 'Duke O Death',      rent = 500, cost = 27500,  category = 'Support Level 10', requiredLevel = 10, img = 'images/vehicles/dukes2.png' },
    -- Support Level 15 vehicles
    -- Prices added for previously zero‑cost vehicles to ensure they are not free.
    { name = 'Phantom Wedge',     rent = 500, cost = 30000, category = 'Support Level 15', requiredLevel = 15, img = 'images/vehicles/phantom3.png' },
    { name = 'Turreted Limo',     rent = 500, cost = 30000, category = 'Support Level 15', requiredLevel = 15, img = 'images/vehicles/limo2.png' },
    { name = 'Armoured Karuma',   rent = 1000, cost = 35000, category = 'Support Level 15', requiredLevel = 15, img = 'images/vehicles/kuruma2.png' },
    -- Helicopter Level 5 vehicles
    { name = 'Frogger',           rent = 500, cost = 25000, category = 'Helicopter Level 5', requiredLevel = 5, img = 'images/vehicles/frogger.png' },
    -- Helicopter Level 10 vehicles
    { name = 'Conada',            rent = 600, cost = 30000, category = 'Helicopter Level 10', requiredLevel = 10, img = 'images/vehicles/conada.png' },
    { name = 'Buzzard',           rent = 600, cost = 30000, category = 'Helicopter Level 10', requiredLevel = 10, img = 'images/vehicles/buzzard.png' },
    { name = 'Cargobob',          rent = 700, cost = 35000, category = 'Helicopter Level 10', requiredLevel = 10, img = 'images/vehicles/cargobob.png' },
    -- Helicopter Level 15 vehicles
    { name = 'Valkyrie',          rent = 800, cost = 40000, category = 'Helicopter Level 15', requiredLevel = 15, img = 'images/vehicles/valkyrie.png' },
    -- VIP vehicles (require VIP rank)
    -- Prestige vehicles are unlocked via prestige tokens rather than money.
    -- These entries are marked with `prestigeOnly = true` to indicate to the
    -- UI that they should display "PRESTIGE ONLY" until the player unlocks
    -- them via the prestige shop.  Do not set `vipOnly` for these items.
    { name = 'Ramp Buggy',        rent = 0,   cost = 0,      category = 'Prestige', prestigeOnly = true, requiredLevel = 0, img = 'images/vehicles/dune4.png' },
    { name = 'Insurgent2',        rent = 0,   cost = 0,      category = 'Prestige', prestigeOnly = true, requiredLevel = 0, img = 'images/vehicles/insurgent2.png' },
    -- VIP‑only vehicle.  This car can only be purchased/spawned by
    -- players who possess the configured Discord role.  When a user
    -- becomes VIP they gain access to this vehicle automatically.  It
    -- is free to rent and free to own.  The model used for spawning
    -- this vehicle is mapped in the spawnVehicle handler below.
    { name = 'VIP Car',           rent = 0,   cost = 0,      category = 'VIP', vipOnly = true, requiredLevel = 0, img = 'images/vehicles/vipcar.png' }
  }

  -- Recalculate player level requirements for non‑VIP vehicles.  The
  -- custom unlock logic requested by the resource owner is as
  -- follows:
  --   • The very first vehicle is free (requiredLevel = 0).
  --   • The next five vehicles unlock at levels 1 through 5 (i.e. the
  --     2nd vehicle at level 1, 3rd at level 2, 6th at level 5).
  --   • After these initial six, each subsequent vehicle unlocks
  --     alternately every 2 and 3 player levels (e.g. level 7, 10,
  --     12, 15, 17, 20, …).  VIP‑only vehicles are excluded from
  --     this calculation and retain their own lock mechanism.
  do
    local freeVehicleCount = 1 -- number of vehicles available for free
    local earlyUnlockCount = 5 -- vehicles that unlock at levels 1..5 after the free vehicle
    local increments = {2, 3}   -- pattern for later unlocks
    for i, veh in ipairs(vehicles) do
      -- Skip VIP‑only and prestige‑only vehicles when calculating level
      -- requirements.  Prestige vehicles should only be unlocked via
      -- tokens, and VIP vehicles use the Discord role gating.
      if not veh.vipOnly and not veh.prestigeOnly then
        if i == 1 then
          -- First vehicle is free
          veh.requiredLevel = 0
        elseif i <= freeVehicleCount + earlyUnlockCount then
          -- Next `earlyUnlockCount` vehicles unlock at successive levels
          -- Example: vehicle 2 → level 1, vehicle 3 → level 2, …, vehicle 6 → level 5
          veh.requiredLevel = i - 1
        else
          -- Remaining vehicles unlock alternately every 2 and 3 levels.
          local indexAfter = i - (freeVehicleCount + earlyUnlockCount)
          local currentLevel = earlyUnlockCount
          for j = 1, indexAfter do
            local inc = increments[((j - 1) % #increments) + 1]
            currentLevel = currentLevel + inc
          end
          veh.requiredLevel = currentLevel
        end
      end
    end
  end

  -- Request fresh data from server
  TriggerServerEvent('koth:getMoneyForVehicleShop', vehicles)
end)

--[[
  Handle interaction with the prestige shop ped.  When the player
  presses E on the prestige ped spawned as part of the team ped
  spawns, the interaction loop in this file triggers the
  koth:openPrestigeMenu event.  To avoid immediately prestiging
  without confirmation, we simply request the player's prestige
  token data from the server.  The server responds via
  koth:receivePrestigeData, after which the NUI is opened with
  the prestige menu.  See html/script.js for client‑side UI logic.
]]
RegisterNetEvent('koth:openPrestigeMenu')
AddEventHandler('koth:openPrestigeMenu', function()
  -- Ask the server for the player's current prestige rank and token
  -- counts.  The response triggers the NUI to display the
  -- prestige shop.  We do not directly prestige here.
  TriggerServerEvent('koth:getPrestigeData')
end)

-- Class menu - triggered by ped interaction
RegisterNetEvent('koth:openClassMenu', function(items)
  print('[KOTH] Opening class menu')

  -- Get player level
  local playerLevel = playerStats and playerStats.level or 1

  -- Use provided items or exact classes from the image
  local classes = items or {
    {
      id = 'assault',
      name = 'Assault',
      unlock = 'Unlocked',
      img = 'images/classes/assault.png',
      requiredLevel = 1
    },
    {
      id = 'medic',
      name = 'Medic',
      unlock = playerLevel >= 5 and 'Unlocked' or 'Unlock at level 5',
      img = 'images/classes/medic.png',
      requiredLevel = 5
    },
    {
      id = 'engineer',
      name = 'Engineer',
      unlock = playerLevel >= 15 and 'Unlocked' or 'Unlock at level 15',
      img = 'images/classes/engineer.png',
      requiredLevel = 15
    },
    {
      id = 'heavy',
      name = 'Heavy',
      unlock = playerLevel >= 25 and 'Unlocked' or 'Unlock at level 25',
      img = 'images/classes/heavy.png',
      requiredLevel = 25
    },
    {
      id = 'scout',
      name = 'Scout',
      unlock = playerLevel >= 35 and 'Unlocked' or 'Unlock at level 35',
      img = 'images/classes/scout.png',
      requiredLevel = 35
    },
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getDataForClassShop', classes)
end)

-- Attachment menu - triggered by ped interaction
RegisterNetEvent('koth:openAttachmentMenu', function()
  print('[KOTH] Opening attachment menu')

  -- Check if player has a weapon in hotbar slot 1
  local playerPed = PlayerPedId()
  local currentWeapon = GetSelectedPedWeapon(playerPed)

  if currentWeapon == GetHashKey('WEAPON_UNARMED') then
    -- Show error message
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("You need a weapon equipped to purchase attachments!")
    EndTextCommandThefeedPostTicker(false, true)
    return
  end

  -- Request attachment menu from server
  TriggerServerEvent('koth:getAttachmentMenu', currentWeapon)
end)

-- Receive attachment menu data from server
RegisterNetEvent('koth:showAttachmentMenu', function(data)
  print('[KOTH] Received attachment menu data:', json.encode(data))

  -- Send message to the attachment menu UI
  SendNUIMessage({
    action = 'showAttachmentMenu',
    attachments = data.attachments,
    weaponName = data.weaponName,
    money = data.money
  })
  openUIFocus()
end)

-- Vehicle purchase callbacks
RegisterNUICallback('buyVehicle', function(data, cb)
  print('[KOTH] Buying vehicle:', data.name or 'none', 'price:', data.price or 'none')
  if data and data.name then
    -- Send price data with the purchase
    TriggerServerEvent('koth:buyVehicle', {
      name = data.name,
      price = tonumber(data.price) or tonumber(data.cost) or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  closeUIFocus()
  cb('ok')
end)

RegisterNUICallback('rentVehicle', function(data, cb)
  print('[KOTH] Renting vehicle:', data.name or 'none', 'price:', data.price or 'none')
  if data and data.name then
    -- Send price data with the rental
    TriggerServerEvent('koth:rentVehicle', {
      name = data.name,
      price = tonumber(data.price) or tonumber(data.rent) or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  closeUIFocus()
  cb('ok')
end)

-- Class selection callback - now shows weapon selection
RegisterNUICallback('selectClass', function(data, cb)
  print('[KOTH] Class selected:', data.id or 'none')
  if data and data.id then
    -- Check if player has required level for this class
    local playerLevel = playerStats and playerStats.level or 1
    local requiredLevels = {
      assault = 1,
      medic = 5,
      engineer = 15,
      heavy = 25,
      scout = 35
    }

    local requiredLevel = requiredLevels[data.id] or 1
    if playerLevel < requiredLevel then
      -- Show error message
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('Class locked! Requires level %d (you are level %d)'):format(requiredLevel, playerLevel))
      EndTextCommandThefeedPostTicker(false, true)
      cb('ok')
      return
    end

    -- Trigger class selection event for koth_classes
    TriggerEvent('koth:classSelected', data.id)
    -- Define class-specific weapons
    local classWeapons = {
      assault = {
        -- Primary weapons (Assault Rifles)
        { weapon = 'WEAPON_CARBINERIFLE', name = 'Carbine Rifle', price = 0, img = 'images/guns/Carbine Rifle.png', category = 'primary', free = true },
        { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk II', price = 3500, img = 'images/guns/Carbine Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_SPECIALCARBINE', name = 'Special Carbine', price = 4000, img = 'images/guns/Special Carbine.png', category = 'primary' },
        { weapon = 'WEAPON_SPECIALCARBINE_MK2', name = 'Special Carbine Mk II', price = 5000, img = 'images/guns/Special Carbine MK II.png', category = 'primary' },
        { weapon = 'WEAPON_ASSAULTRIFLE', name = 'Assault Rifle', price = 3500, img = 'images/guns/Assault Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_ASSAULTRIFLE_MK2', name = 'Assault Rifle Mk II', price = 4500, img = 'images/guns/Assault Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 3000, img = 'images/guns/Bullpup Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE_MK2', name = 'Bullpup Rifle Mk II', price = 4000, img = 'images/guns/Bullpup Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_ADVANCEDRIFLE', name = 'Advanced Rifle', price = 5500, img = 'images/guns/Advanced Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_MILITARYRIFLE', name = 'Military Rifle', price = 6000, img = 'images/guns/Military Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_HEAVYRIFLE', name = 'Heavy Rifle', price = 6500, img = 'images/guns/Heavy Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_COMPACTRIFLE', name = 'Compact Rifle', price = 2500, img = 'images/guns/Compact Rifle.png', category = 'primary' },
        -- Secondary weapons (SMGs)
        { weapon = 'WEAPON_SMG', name = 'SMG', price = 1500, img = 'images/guns/SMG.png', category = 'secondary' },
        { weapon = 'WEAPON_SMG_MK2', name = 'SMG Mk II', price = 2500, img = 'images/guns/SMG MK II.png', category = 'secondary' },
        { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 2000, img = 'images/guns/Combat PDW.png', category = 'secondary' },
        { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 2500, img = 'images/guns/Assault SMG.png', category = 'secondary' },
        { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 1000, img = 'images/guns/Micro SMG.png', category = 'secondary' },
        { weapon = 'WEAPON_MACHINEPISTOL', name = 'Machine Pistol', price = 1200, img = 'images/guns/Machine Pistol.png', category = 'secondary' },
        -- Additional SMG options requested by the server owner.  These
        -- classic SMGs broaden the assault class's secondary selection.
        { weapon = 'WEAPON_MP5', name = 'MP5', price = 1800, img = 'images/guns/SMG.png', category = 'secondary' },
        { weapon = 'WEAPON_MP7', name = 'MP7', price = 2000, img = 'images/guns/SMG MK II.png', category = 'secondary' },
        { weapon = 'WEAPON_MP9A', name = 'MP9A', price = 1600, img = 'images/guns/Micro SMG.png', category = 'secondary' },
        { weapon = 'WEAPON_P90', name = 'P90', price = 2200, img = 'images/guns/Assault SMG.png', category = 'secondary' },
        -- Sidearm: only the Desert Eagle (Pistol .50) is available for assault
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = 'images/guns/Pistol .50.png', category = 'sidearm' },
        -- Throwables removed for assault class (grenades and bombs are not available)
        -- VIP-exclusive weapons for assault class.  These weapons are only visible
        -- to players who have purchased a VIP rank through Tebex and have the
        -- appropriate Discord role.  They are categorised under 'vip' so the
        -- UI can group them separately.  Prices are set to zero because the
        -- cost of these items is included with the VIP package.
        -- Additional rifles requested: FAMAS Mk II, SCAR Mk II and the British L85A2.
        { weapon = 'WEAPON_FAMASMK2', name = 'FAMAS Mk II', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_SCARMK2', name = 'SCAR Mk II', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_L85A2', name = 'L85A2', price = 0, img = 'images/guns/Advanced Rifle.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_SCAR', name = 'SCAR', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_M4A1', name = 'M4A1', price = 0, img = 'images/guns/M4A1.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_FAMAS', name = 'FAMAS', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true }
      },
      medic = {
        -- Primary weapons (SMGs/Shotguns)
        { weapon = 'WEAPON_SMG', name = 'SMG', price = 0, img = 'images/guns/SMG.png', category = 'primary', free = true },
        { weapon = 'WEAPON_SMG_MK2', name = 'SMG Mk II', price = 2500, img = 'images/guns/SMG MK II.png', category = 'primary' },
        { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 1000, img = 'images/guns/Micro SMG.png', category = 'primary' },
        { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 2000, img = 'images/guns/Combat PDW.png', category = 'primary' },
        { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 2500, img = 'images/guns/Assault SMG.png', category = 'primary' },
        { weapon = 'WEAPON_SWEEPERSHOTGUN', name = 'Sweeper Shotgun', price = 3000, img = 'images/guns/Sweeper Shotgun.png', category = 'primary' },
        { weapon = 'WEAPON_COMBATSHOTGUN', name = 'Combat Shotgun', price = 3500, img = 'images/guns/Combat Shotgun.png', category = 'primary' },
        -- Sidearm: only the Desert Eagle (Pistol .50) is available for medic
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = 'images/guns/Pistol .50.png', category = 'sidearm' },
        -- Throwables removed for medic class
        -- VIP-exclusive weapons for medic class (same as assault).  These entries
        -- mirror the assault class but are grouped under the medic loadout so
        -- medics can purchase them if they are VIP.  The vipOnly flag allows
        -- the UI to hide them for non-VIP players.
        { weapon = 'WEAPON_SCAR', name = 'SCAR', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_M4A1', name = 'M4A1', price = 0, img = 'images/guns/M4A1.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_FAMAS', name = 'FAMAS', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true }
      },
      engineer = {
        -- Primary weapons (Compact/Tech Rifles)
        { weapon = 'WEAPON_COMPACTRIFLE', name = 'Compact Rifle', price = 0, img = 'images/guns/Compact Rifle.png', category = 'primary', free = true },
        { weapon = 'WEAPON_CARBINERIFLE', name = 'Carbine Rifle', price = 3000, img = 'images/guns/Carbine Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk II', price = 3500, img = 'images/guns/Carbine Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 3000, img = 'images/guns/Bullpup Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE_MK2', name = 'Bullpup Rifle Mk II', price = 4000, img = 'images/guns/Bullpup Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_ADVANCEDRIFLE', name = 'Advanced Rifle', price = 5500, img = 'images/guns/Advanced Rifle.png', category = 'primary' },
        -- Secondary weapon: only the Desert Eagle (Pistol .50) is available for engineer
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = 'images/guns/Pistol .50.png', category = 'secondary' },
        -- Throwables removed for engineer class
        -- VIP-exclusive weapons for engineer class.  These are the same VIP
        -- rifles available to assault and medic classes.  They remain hidden
        -- for non-VIP players via the vipOnly flag.
        { weapon = 'WEAPON_SCAR', name = 'SCAR', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_M4A1', name = 'M4A1', price = 0, img = 'images/guns/M4A1.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_FAMAS', name = 'FAMAS', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true }
      },
      heavy = {
        -- Primary weapons (LMGs/Shotguns) - MG is now the first free weapon
        { weapon = 'WEAPON_MG', name = 'MG', price = 0, img = 'images/guns/MG.png', category = 'primary', free = true },
        { weapon = 'WEAPON_COMBATMG', name = 'Combat MG', price = 4500, img = 'images/guns/Combat MG.png', category = 'primary' },
        { weapon = 'WEAPON_COMBATMG_MK2', name = 'Combat MG Mk II', price = 5000, img = 'images/guns/Combat MG MK II.png', category = 'primary' },
        { weapon = 'WEAPON_GUSENBERG', name = 'Gusenberg Sweeper', price = 3500, img = 'images/guns/Gusenberg Sweeper.png', category = 'primary' },
        { weapon = 'WEAPON_MINIGUN', name = 'Minigun', price = 10000, img = 'images/guns/Minigun.png', category = 'primary' },
        { weapon = 'WEAPON_HEAVYSHOTGUN', name = 'Heavy Shotgun', price = 4000, img = 'images/guns/Heavy Shotgun.png', category = 'primary' },
        { weapon = 'WEAPON_ASSAULTSHOTGUN', name = 'Assault Shotgun', price = 4500, img = 'images/guns/Assault Shotgun.png', category = 'primary' },
        -- Secondary weapons (Shotguns)
        { weapon = 'WEAPON_PUMPSHOTGUN', name = 'Pump Shotgun', price = 2000, img = 'images/guns/Pump Shotgun.png', category = 'secondary' },
        { weapon = 'WEAPON_PUMPSHOTGUN_MK2', name = 'Pump Shotgun Mk II', price = 3000, img = 'images/guns/Pump Shotgun MK II.png', category = 'secondary' },
        { weapon = 'WEAPON_COMBATSHOTGUN', name = 'Combat Shotgun', price = 3500, img = 'images/guns/Combat Shotgun.png', category = 'secondary' },
        { weapon = 'WEAPON_SWEEPERSHOTGUN', name = 'Sweeper Shotgun', price = 3000, img = 'images/guns/Sweeper Shotgun.png', category = 'secondary' },
        -- Sidearm: only the Desert Eagle (Pistol .50) is available for heavy
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = 'images/guns/Pistol .50.png', category = 'sidearm' },
        -- Throwables removed for heavy class
        -- VIP-exclusive weapons for heavy class.  While heavy already has
        -- access to high-powered weapons, these assault rifles are included in
        -- the VIP package and flagged as vipOnly.
        { weapon = 'WEAPON_SCAR', name = 'SCAR', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_M4A1', name = 'M4A1', price = 0, img = 'images/guns/M4A1.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_FAMAS', name = 'FAMAS', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true }
      },
      scout = {
        -- Primary weapons (Snipers/DMRs)
        { weapon = 'WEAPON_MARKSMANRIFLE', name = 'Marksman Rifle', price = 0, img = 'images/guns/Marksman Rifle.png', category = 'primary', free = true },
        { weapon = 'WEAPON_MARKSMANRIFLE_MK2', name = 'Marksman Rifle Mk II', price = 6500, img = 'images/guns/Marksman Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_SNIPERRIFLE', name = 'Sniper Rifle', price = 5000, img = 'images/guns/Sniper Rifle.png', category = 'primary' },
        { weapon = 'WEAPON_SNIPERRIFLE_MK2', name = 'Sniper Rifle Mk II', price = 6000, img = 'images/guns/Sniper Rifle MK II.png', category = 'primary' },
        { weapon = 'WEAPON_HEAVYSNIPER', name = 'Heavy Sniper', price = 7000, img = 'images/guns/Heavy Sniper.png', category = 'primary' },
        { weapon = 'WEAPON_HEAVYSNIPER_MK2', name = 'Heavy Sniper Mk II', price = 8500, img = 'images/guns/Heavy Sniper MK II.png', category = 'primary' },
        -- Sidearm: only the Desert Eagle (Pistol .50) is available for scout
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = 'images/guns/Pistol .50.png', category = 'sidearm' },
        -- Throwables removed for scout class
        -- VIP-exclusive weapons for scout class.  Scouts typically use long
        -- range rifles, but these VIP assault rifles give them additional
        -- flexibility.  They are hidden behind the vipOnly flag.
        { weapon = 'WEAPON_SCAR', name = 'SCAR', price = 0, img = 'images/guns/SCAR.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_M4A1', name = 'M4A1', price = 0, img = 'images/guns/M4A1.png', category = 'vip', vipOnly = true },
        { weapon = 'WEAPON_FAMAS', name = 'FAMAS', price = 0, img = 'images/guns/FAMAS.png', category = 'vip', vipOnly = true }
      }
    }

    -- Before sending the weapons back to the UI we need to normalise
    -- the image file names.  We have renamed all weapon images in
    -- `html/images/guns` to use underscores instead of spaces,
    -- hyphens and periods (e.g. "Carbine Rifle.png" became
    -- "Carbine_Rifle.png").  To ensure the UI references the
    -- correct filename we dynamically convert each `item.img`
    -- accordingly.  This avoids issues with spaces being incorrectly
    -- encoded or ignored by the NUI.
    for _, classTable in pairs(classWeapons) do
      for _, item in ipairs(classTable) do
        if type(item.img) == 'string' then
          -- Extract the directory prefix and filename
          local prefix, filename = item.img:match("(.-)([^/]+)$")
          if prefix and filename then
            local name, ext = filename:match("(.+)%.(%w+)$")
            if name and ext then
              -- Replace spaces, hyphens and periods with underscores
              local slug = name
              slug = slug:gsub('[%. %-]', '_')
              slug = slug:gsub(' ', '_')
              -- Collapse any consecutive underscores
              slug = slug:gsub('_+', '_')
              -- Normalise 'mk' to uppercase 'MK' for Mk II names
              slug = slug:gsub('Mk_', 'MK_')
              slug = slug:gsub('mk_', 'MK_')
              item.img = prefix .. slug .. '.' .. ext
            end
          end
        end
      end
    end

    -- Get weapons for the selected class
    local weapons = classWeapons[data.id] or {}

    -- Request fresh money data directly from server for weapon shop
    print('[KOTH] Requesting fresh money data for weapon shop...')
    TriggerServerEvent('koth:getMoneyForWeaponShop', data.id, weapons)
  end
  cb('ok')
end)

-- Weapon selection callback
RegisterNUICallback('selectWeapon', function(data, cb)
  print('[KOTH] Weapon selected:', data.weapon or 'none', 'for class:', data.class or 'none', 'price:', data.price or 'none', 'type:', data.purchaseType or 'buy')

-- Daily reward status/result handlers
RegisterNetEvent('koth:dailyStatus', function(data)
  if data and data.available and data.preview then
    -- Ask UI to show panel
    SendNUIMessage({ action = 'showDailyReward', preview = data.preview })
    openUIFocus()
  else
    -- Not available; ensure it's hidden
    SendNUIMessage({ action = 'hideDailyReward' })
  end
end)

RegisterNetEvent('koth:dailyClaimed', function(result)
  -- Hide modal and show a small confirmation via kill reward popup style
  SendNUIMessage({ action = 'hideDailyReward' })
  closeUIFocus()
  if result then
    -- Optional toast/notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(("Daily claimed: +$%s, +%s XP%s"):format(
      tostring(result.money or 0), tostring(result.xp or 0), result.isVip and " (VIP)" or ""))
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

  if data and data.weapon and data.class then
    TriggerServerEvent('koth:selectLoadout', data.class, data.weapon, data.price, data.purchaseType)
  end
  -- Don't hide the UI immediately - let the server response handle it
  cb('ok')
end)

-- Handle weapon shop closing from server
RegisterNetEvent('koth:closeWeaponShop', function()
  print('[KOTH] Closing weapon shop UI')
  SendNUIMessage({ action='hideAll' })
  closeUIFocus()
end)

-- Receive weapon from server and give to player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price, isRental)
  print(('[KOTH] Receiving weapon: %s for class: %s (price: $%s, rental: %s)'):format(weapon or 'none', classId or 'none', tostring(price or 'free'), tostring(isRental)))

  if weapon then
    local playerPed = PlayerPedId()

    -- Remove all weapons first
    RemoveAllPedWeapons(playerPed, true)

    -- Give the selected weapon with ammo
    local weaponHash = GetHashKey(weapon)
    GiveWeaponToPed(playerPed, weaponHash, 250, false, true)

    -- Set as current weapon
    SetCurrentPedWeapon(playerPed, weaponHash, true)

    print(('[KOTH] Successfully equipped %s'):format(weapon))

    -- Record this purchase as the player's previous loadout.  We store
    -- the weapon name and its price so that the player can later
    -- repurchase the same loadout after death.  Attachments will be
    -- recorded separately when purchased.  If we are not in the
    -- process of repurchasing a loadout (i.e., the weapon is being
    -- bought fresh from the shop), reset the attachments list.
    previousLoadout.weapon = weapon
    previousLoadout.weaponPrice = price or 0
    previousLoadout.purchaseType = (isRental and 'rent' or 'buy')
    previousLoadout.owned = (not isRental)
    if not isRepurchasingLoadout then
      previousLoadout.attachments = {}
    end

    -- Add weapon to hotbar slot 1
    if exports['hotbar'] then
      -- Get weapon display name (remove WEAPON_ prefix and format nicely)
      local weaponDisplayName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
      -- Capitalize first letter of each word
      weaponDisplayName = weaponDisplayName:gsub("(%a)([%w_']*)", function(first, rest)
        return first:upper() .. rest:lower()
      end)

      -- Get weapon image path based on weapon name
      local weaponImageMap = {
        -- Pistols
        ['WEAPON_PISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_PISTOL_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_COMBATPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_APPISTOL'] = "images/weapon_appistol.png",
        ['WEAPON_PISTOL50'] = "images/weapon_pistol50.png",
        ['WEAPON_SNSPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_SNSPISTOL_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_HEAVYPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_VINTAGEPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_CERAMICPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_DOUBLEACTION'] = "images/weapon_pistol50.png",
        ['WEAPON_REVOLVER'] = "images/weapon_pistol50.png",
        ['WEAPON_REVOLVER_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_NAVYREVOLVER'] = "images/weapon_pistol50.png",
        -- SMGs
        ['WEAPON_MICROSMG'] = "images/weapon_microsmg.png",
        ['WEAPON_SMG'] = "images/weapon_smg.png",
        ['WEAPON_SMG_MK2'] = "images/weapon_smg.png",
        ['WEAPON_ASSAULTSMG'] = "images/weapon_assaultsmg.png",
        ['WEAPON_COMBATPDW'] = "images/weapon_combatpdw.png",
        ['WEAPON_MACHINEPISTOL'] = "images/weapon_machinepistol.png",
        ['WEAPON_MINISMG'] = "images/weapon_minismg.png",
        -- Assault Rifles
        ['WEAPON_ASSAULTRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_ASSAULTRIFLE_MK2'] = "images/weapon_assaultrifle_mk2.png",
        ['WEAPON_CARBINERIFLE'] = "images/weapon_m4a1.png",
        ['WEAPON_CARBINERIFLE_MK2'] = "images/weapon_m4a1.png",
        ['WEAPON_ADVANCEDRIFLE'] = "images/weapon_advancedrifle.png",
        ['WEAPON_SPECIALCARBINE'] = "images/weapon_specialcarbine.png",
        ['WEAPON_SPECIALCARBINE_MK2'] = "images/weapon_specialcarbine_mk2.png",
        ['WEAPON_BULLPUPRIFLE'] = "images/weapon_bullpuprifle.png",
        ['WEAPON_BULLPUPRIFLE_MK2'] = "images/weapon_bullpuprifle_mk2.png",
        ['WEAPON_COMPACTRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_MILITARYRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_HEAVYRIFLE'] = "images/weapon_assaultrifle.png",
        -- LMGs
        ['WEAPON_MG'] = "images/weapon_combatmg.png",
        ['WEAPON_COMBATMG'] = "images/weapon_combatmg.png",
        ['WEAPON_COMBATMG_MK2'] = "images/weapon_combatmg.png",
        ['WEAPON_GUSENBERG'] = "images/weapon_combatmg.png",
        ['WEAPON_MINIGUN'] = "images/weapon_combatmg.png",
        -- Snipers
        ['WEAPON_SNIPERRIFLE'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_SNIPERRIFLE_MK2'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_HEAVYSNIPER'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_HEAVYSNIPER_MK2'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_MARKSMANRIFLE'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_MARKSMANRIFLE_MK2'] = "images/weapon_marksmanrifle.png",
        -- Shotguns
        ['WEAPON_PUMPSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_PUMPSHOTGUN_MK2'] = "images/weapon_assaultrifle.png",
        ['WEAPON_COMBATSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_ASSAULTSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_HEAVYSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_SWEEPERSHOTGUN'] = "images/weapon_assaultrifle.png",
        -- Throwables (using default image)
        ['WEAPON_GRENADE'] = "images/weapon_pistol50.png",
        ['WEAPON_STICKYBOMB'] = "images/weapon_pistol50.png",
        ['WEAPON_PIPEBOMB'] = "images/weapon_pistol50.png",
        ['WEAPON_BZGAS'] = "images/weapon_pistol50.png",
        ['WEAPON_MOLOTOV'] = "images/weapon_pistol50.png",
        ['WEAPON_PROXMINE'] = "images/weapon_pistol50.png"
      }

      local weaponIcon = weaponImageMap[weapon] or "images/weapon_pistol50.png"

      -- Call hotbar export to set weapon in slot 1
      exports['hotbar']:SetHotbarItem(1, weaponDisplayName, weaponIcon, 250, weaponHash)
      print(('[KOTH] Added %s to hotbar slot 1 with icon %s'):format(weaponDisplayName, weaponIcon))
    else
      print('[KOTH] Hotbar resource not found - weapon not added to hotbar')
    end

    -- Show notification with price
    BeginTextCommandThefeedPost("STRING")
    local weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
    local message = ('Purchased %s for $%s'):format(weaponName, price or '0')
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- Purchase result handler
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- If purchase failed, keep the shop open
  if not success then
    -- You could add specific UI feedback here
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Attachment purchase callback
RegisterNUICallback('purchaseAttachment', function(data, cb)
  print('[KOTH] Purchasing attachment:', data.name or 'none', 'component:', data.component or 'none', 'price:', data.price or 'none')
  if data and data.name and data.component then
    -- Forward the purchase request to the server
    TriggerServerEvent('koth:purchaseAttachment', data)
    -- Append this attachment to the previous loadout so it can be
    -- repurchased later.  Store the name, component and price fields.
    previousLoadout.attachments = previousLoadout.attachments or {}
    table.insert(previousLoadout.attachments, {
      name = data.name,
      component = data.component,
      price = tonumber(data.price) or 0
    })
  end
  -- Hide the NUI after purchase
  SendNUIMessage({ action='hideAll' })
  closeUIFocus()
  cb('ok')
end)

-- Apply attachment to weapon
RegisterNetEvent('koth:applyAttachment', function(component)
  print('[KOTH] Applying attachment component:', component)

  local playerPed = PlayerPedId()
  local currentWeapon = GetSelectedPedWeapon(playerPed)

  if currentWeapon ~= GetHashKey('WEAPON_UNARMED') then
    -- Apply the attachment component to the current weapon
    GiveWeaponComponentToPed(playerPed, currentWeapon, GetHashKey(component))
    print('[KOTH] Successfully applied attachment component', component, 'to weapon', currentWeapon)

    -- Update hotbar if available
    if exports['hotbar'] then
      -- Get weapon display name
      local weaponName = GetWeaponDisplayName(currentWeapon)
      local weaponIcon = GetWeaponIcon(currentWeapon)
      local ammo = GetAmmoInPedWeapon(playerPed, currentWeapon)

      -- Update hotbar slot 1 with the modified weapon
      exports['hotbar']:SetHotbarItem(1, weaponName, weaponIcon, ammo, currentWeapon)
      print('[KOTH] Updated hotbar with modified weapon')
    end
  else
    print('[KOTH] No weapon equipped to apply attachment to')
  end
end)

-- Helper function to get weapon display name
function GetWeaponDisplayName(weaponHash)
  local weaponName = 'Unknown Weapon'

  -- Convert hash to weapon name
  for weapon, hash in pairs({
    ['WEAPON_PISTOL'] = GetHashKey('WEAPON_PISTOL'),
    ['WEAPON_COMBATPISTOL'] = GetHashKey('WEAPON_COMBATPISTOL'),
    ['WEAPON_APPISTOL'] = GetHashKey('WEAPON_APPISTOL'),
    ['WEAPON_ASSAULTRIFLE'] = GetHashKey('WEAPON_ASSAULTRIFLE'),
    ['WEAPON_CARBINERIFLE'] = GetHashKey('WEAPON_CARBINERIFLE'),
    ['WEAPON_SMG'] = GetHashKey('WEAPON_SMG'),
    ['WEAPON_SNIPERRIFLE'] = GetHashKey('WEAPON_SNIPERRIFLE')
  }) do
    if hash == weaponHash then
      weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
      weaponName = weaponName:gsub("(%a)([%w_']*)", function(first, rest)
        return first:upper() .. rest:lower()
      end)
      break
    end
  end

  return weaponName
end

-- Helper function to get weapon icon
function GetWeaponIcon(weaponHash)
  local weaponImageMap = {
    [GetHashKey('WEAPON_PISTOL')] = "images/weapon_pistol50.png",
    [GetHashKey('WEAPON_COMBATPISTOL')] = "images/weapon_pistol50.png",
    [GetHashKey('WEAPON_APPISTOL')] = "images/weapon_appistol.png",
    [GetHashKey('WEAPON_ASSAULTRIFLE')] = "images/weapon_assaultrifle.png",
    [GetHashKey('WEAPON_CARBINERIFLE')] = "images/weapon_m4a1.png",
    [GetHashKey('WEAPON_SMG')] = "images/weapon_smg.png",
    [GetHashKey('WEAPON_SNIPERRIFLE')] = "images/weapon_marksmanrifle.png"
  }

  return weaponImageMap[weaponHash] or "images/weapon_pistol50.png"
end

-- Close menu callback
RegisterNUICallback('closeMenu', function(data, cb)
  print('[KOTH] Menu closed')
  SendNUIMessage({ action='hideAll' })
  closeUIFocus()
  cb('ok')
end)

-- Vehicle spawning handler
RegisterNetEvent('koth:spawnVehicle', function(vehicleName, purchaseType, price)
  print(('[KOTH] Spawning vehicle: %s (%s for $%s)'):format(vehicleName or 'none', purchaseType or 'unknown', price or '0'))

  if vehicleName then
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)

    -- Vehicle name mapping (simplified)
    -- Mapping of vehicle display names to in‑game model names.  This
    -- list includes all vehicles offered in the vehicle shop, along
    -- with a few legacy entries.  When adding new vehicles to the
    -- shop, ensure they are represented here so they spawn correctly.
    local vehicleModels = {
      -- Base/legacy mappings
      ['Blista']             = 'blista',
      ['Futo']               = 'futo',
      ['Elegy']              = 'elegy2',
      ['Insurgent']          = 'insurgent',
      ['Technical']          = 'technical',
      ['Sandking XL']        = 'sandking',
      ['Mesa']               = 'mesa',
      -- Buzzard now spawns as the AH‑6 Little Bird variant (model name `ah6`).  This
      -- change was requested by the user to replace the default Buzzard model.
      ['Buzzard']            = 'ah6',
      ['Savage']             = 'savage',
      ['Rhino Tank']         = 'rhino',
      ['Hydra']              = 'hydra',
      -- KOTH vehicle shop mappings
      -- Replace the Crusader with the Mesa XL off‑road variant.  This
      -- responds to the user's request to spawn the “Mesa XL” instead
      -- of the standard Crusader army jeep.
      ['Crusader']           = 'mesaxl',
      ['BF400']              = 'bf400',
      -- Replace the Manchez with the Manchez3 dirt bike variant.
      ['Manchez']            = 'manchez3',
      ['M9395']              = 'barracks3', -- closest base game equivalent
      ['Kuruma']             = 'kuruma',
      ['Outlaw']             = 'outlaw',
      ['Kamacho']            = 'kamacho',
      ['Rebla']              = 'rebla',
      -- Replace the Sultan sports sedan with the Flash GT rally car.
      ['Flash GT']           = 'flashgt',
      -- New vehicles added to the shop/UI
      ['Baller5']            = 'baller5',
      ['I-Wagen']            = 'iwagen',
      ['Astron']             = 'astron',
      ['Calico']             = 'calico',
      ['Havok']              = 'havok',
      -- Replace the Rumpo delivery van with its newer Rumpo3 variant.
      ['Rumpo']              = 'rumpo3',
      -- Replace the Wastelander with the Insurgent2 armoured truck.
      ['Wastelander']        = 'insurgent2',
      -- Prestige mapping: allow spawning the Insurgent2 directly using
      -- the display name "Insurgent2".  This enables the prestige vehicle
      -- purchase system to pass "Insurgent2" as the vehicle name and
      -- have it resolve to the correct model.  You can also reference it
      -- as "Prestige Insurgent" if desired.
      ['Insurgent2']         = 'insurgent2',
      ['Prestige Insurgent'] = 'insurgent2',
      ['Dune FAV']           = 'dune3', -- off‑road dune buggy with MG
      ['Duke O Death']       = 'dukes2',
      -- Swap the Phantom Wedge for the Phantom2 semi‑truck.
      ['Phantom Wedge']      = 'phantom2',
      ['Turreted Limo']      = 'limo2',
      ['Frogger']            = 'frogger',
      ['Conada']             = 'conada',
      -- Use the MH‑6 Little Bird (unarmed version) instead of the AH‑6.
      ['Buzzard']            = 'mh6',
      -- Use the Cargobob2 variant (has different paint scheme).  If you
      -- decide to change this further, update this mapping accordingly.
      -- Swap the Cargobob for the Chinook cargo helicopter.
      ['Cargobob']           = 'chinook',
      ['Valkyrie']           = 'valkyrie',
      ['Ramp Buggy']         = 'dune4',
      ['Kuruma (Armored)']   = 'kuruma2',
      -- Synonyms for backwards compatibility
      ['Armored Kuruma']     = 'kuruma2'
    ,['VIP Car']           = 'gsttoros1'
    }

    local modelName = vehicleModels[vehicleName] or vehicleName:lower()
    local modelHash = GetHashKey(modelName)

    -- Request model
    RequestModel(modelHash)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(modelHash) and GetGameTimer() < timeout do
      Citizen.Wait(1)
    end

    if not HasModelLoaded(modelHash) then
      print(('[KOTH] Initial model load failed for %s, retrying...'):format(modelName))
      RequestModel(modelHash)
      local retryTimeout = GetGameTimer() + 5000
      while not HasModelLoaded(modelHash) and GetGameTimer() < retryTimeout do
        Citizen.Wait(1)
      end
    end

    if HasModelLoaded(modelHash) then
      -- Determine spawn location.  Prefer map‑defined vehicle spawnpoints if present
      -- in the current team's spawn data.  When a matching car or helicopter
      -- spawn exists, use it directly; otherwise fall back to spawning a few
      -- metres away from the team's spawn point (or the player if no team).
      local spawnX, spawnY, spawnZ, spawnHeading
      if playerTeam and teamSpawns and teamSpawns[playerTeam] then
        local info = teamSpawns[playerTeam]
        -- Check if this model is a helicopter.  Use the built‑in native to
        -- detect aircraft so any new models added to the shop are handled
        local isHeli = IsThisModelAHeli(modelHash)
        local vehicleSpawn = nil
        -- For helicopters prefer the heli spawn; for other vehicles use car spawn
        if isHeli and info.heli then
          vehicleSpawn = info.heli
        elseif (not isHeli) and info.car then
          vehicleSpawn = info.car
        end
        if vehicleSpawn then
          -- Use the explicit vehicle spawnpoint from the map file.  Elevate
          -- slightly above ground to avoid the vehicle clipping into the
          -- terrain on spawn.  The heading value rotates the vehicle to the
          -- correct orientation on the spawn pad.
          spawnX = vehicleSpawn.x
          spawnY = vehicleSpawn.y
          spawnZ = (vehicleSpawn.z or info.z) + 1.0
          spawnHeading = vehicleSpawn.heading or info.heading or 0.0
        else
          -- Random offset fallback when no dedicated spawnpoint exists for
          -- this vehicle type.  Offset the vehicle 10–15 metres from the
          -- team spawn heading to avoid colliding with peds or players.
          local baseX, baseY, baseZ = info.x, info.y, info.z
          local baseHeading = info.heading or 0.0
          local distance = math.random(10, 15) + 0.0
          local offX = baseX + math.cos(math.rad(baseHeading)) * distance
          local offY = baseY + math.sin(math.rad(baseHeading)) * distance
          local foundGround, groundZ = GetGroundZFor_3dCoord(offX, offY, baseZ + 50.0, false)
          spawnX = offX
          spawnY = offY
          spawnZ = (foundGround and (groundZ + 1.0) or (baseZ + 1.0))
          spawnHeading = baseHeading
        end
      else
        -- No team information available; spawn relative to the player's
        -- current position just ahead of them.
        local baseX, baseY, baseZ = playerCoords.x, playerCoords.y, playerCoords.z
        local baseHeading = playerHeading
        local distance = 5.0
        local offX = baseX + math.cos(math.rad(baseHeading)) * distance
        local offY = baseY + math.sin(math.rad(baseHeading)) * distance
        local foundGround, groundZ = GetGroundZFor_3dCoord(offX, offY, baseZ + 50.0, false)
        spawnX = offX
        spawnY = offY
        spawnZ = (foundGround and (groundZ + 1.0) or (baseZ + 1.0))
        spawnHeading = baseHeading
      end
      -- Create the vehicle at the calculated spawn location and orientation
      local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, spawnHeading, true, false)

      if DoesEntityExist(vehicle) then
        print(('[KOTH] Vehicle entity created: %s'):format(vehicle))
        -- Ensure vehicle is properly placed on ground
        SetEntityCoords(vehicle, spawnX, spawnY, spawnZ, false, false, false, true)
        SetVehicleOnGroundProperly(vehicle)

        -- Set player as owner
        SetVehicleHasBeenOwnedByPlayer(vehicle, true)
        -- Mark vehicle as player-owned for other resources
        DecorSetBool(vehicle, "PlayerVehicle", true)
        if Entity(vehicle) then
          Entity(vehicle).state:set('playerOwned', true, true)
        end

        -- Debug: Verify the decorator was set
        local decorSet = DecorExistOn(vehicle, "PlayerVehicle") and DecorGetBool(vehicle, "PlayerVehicle")
        print(('[KOTH] Vehicle decorator "PlayerVehicle" set: %s'):format(tostring(decorSet)))

        -- Make vehicle persistent
        SetEntityAsMissionEntity(vehicle, true, true)

        -- Set network ownership
        local netId = NetworkGetNetworkIdFromEntity(vehicle)
        SetNetworkIdCanMigrate(netId, true)
        SetNetworkIdExistsOnAllMachines(netId, true)

        -- Ensure vehicle has proper collision with ground
        SetEntityCollision(vehicle, true, true)

        -- Apply a team-specific colour to the vehicle.  Each team's
        -- vehicles are tinted to match the team colour so that other
        -- players can easily identify who owns a vehicle.  We use custom
        -- RGB colours rather than GTA colour indices to guarantee vivid
        -- primary colours.  If the player hasn't selected a team, no
        -- colour modification is applied.
        if playerTeam then
          -- Initialise mod kit so custom colours take effect
          SetVehicleModKit(vehicle, 0)
          if playerTeam == 'red' then
            SetVehicleCustomPrimaryColour(vehicle, 255, 0, 0)
            SetVehicleCustomSecondaryColour(vehicle, 255, 0, 0)
          elseif playerTeam == 'blue' then
            SetVehicleCustomPrimaryColour(vehicle, 0, 0, 255)
            SetVehicleCustomSecondaryColour(vehicle, 0, 0, 255)
          elseif playerTeam == 'green' then
            SetVehicleCustomPrimaryColour(vehicle, 0, 255, 0)
            SetVehicleCustomSecondaryColour(vehicle, 0, 255, 0)
          end
        end

        -- Wait a moment for physics to settle
        Citizen.Wait(100)

        -- Put player in vehicle
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)

        -- ------------------------------------------------------------------
        -- Vehicle ghost mode
        --
        -- When spawning a vehicle it temporarily becomes semi‑transparent and
        -- non‑colliding with other vehicles and players.  This prevents
        -- spawn pile‑ups and allows players to drive out of congested
        -- areas without causing damage or being blocked.  Collisions with
        -- the world (roads, terrain, props) remain intact so the
        -- vehicle stays grounded.
        do
          -- Make the vehicle semi‑transparent.  150 gives a ghostly look
          -- without making it completely invisible.  Setting the last
          -- argument to false ensures the alpha persists on network clones.
          SetEntityAlpha(vehicle, 150, false)

          -- Create a thread to disable collisions for 8 seconds
          Citizen.CreateThread(function()
            local endTime = GetGameTimer() + 8000
            while GetGameTimer() < endTime do
              -- Disable collision with other vehicles
              for otherVeh in EnumerateVehicles() do
                if otherVeh ~= vehicle and DoesEntityExist(otherVeh) then
                  SetEntityNoCollisionEntity(vehicle, otherVeh, true)
                  SetEntityNoCollisionEntity(otherVeh, vehicle, true)
                end
              end
              -- Disable collision with peds (including players) to prevent
              -- running people over during the grace period
              for ped in EnumeratePeds() do
                -- Skip self ped; collisions between the vehicle and the
                -- driver are handled by the game
                if ped ~= PlayerPedId() and DoesEntityExist(ped) then
                  SetEntityNoCollisionEntity(vehicle, ped, true)
                  SetEntityNoCollisionEntity(ped, vehicle, true)
                end
              end
              -- We intentionally avoid forcing the vehicle back onto
              -- the ground every frame.  Calling SetVehicleOnGroundProperly
              -- repeatedly can interfere with the physics engine and
              -- cause the car to slide or behave erratically.  The
              -- vehicle is already placed on the ground before the
              -- ghost period starts.
              Citizen.Wait(0)
            end
            -- Restore collisions with other vehicles and peds
            for otherVeh in EnumerateVehicles() do
              if otherVeh ~= vehicle and DoesEntityExist(otherVeh) then
                SetEntityNoCollisionEntity(vehicle, otherVeh, false)
                SetEntityNoCollisionEntity(otherVeh, vehicle, false)
              end
            end
            for ped in EnumeratePeds() do
              if ped ~= PlayerPedId() and DoesEntityExist(ped) then
                SetEntityNoCollisionEntity(vehicle, ped, false)
                SetEntityNoCollisionEntity(ped, vehicle, false)
              end
            end
            -- Reset vehicle alpha to opaque
            SetEntityAlpha(vehicle, 255, false)
          end)
        end

        print(('[KOTH] Successfully spawned %s at ground level - marked as player-owned'):format(vehicleName))

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        -- Handle price being a table or number
        local priceValue = price
        if type(price) == 'table' then
          priceValue = price.price or price.cost or price.rent or '0'
        end
        local message = ('%s %s for $%s'):format(purchaseType == 'rent' and 'Rented' or 'Purchased', vehicleName, tostring(priceValue))
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
      else
        print(('[KOTH] Failed to create vehicle: %s'):format(vehicleName))
      end

      SetModelAsNoLongerNeeded(modelHash)
    else
      print(('[KOTH] Failed to load model: %s'):format(modelName))
    end
  end
end)

-- PED SPAWNING SYSTEM
local spawnedPeds = {}

-- Helper function to delete all spawned peds.  When the map rotates, the old
-- team selection peds should be removed to prevent duplicates and conflicting
-- interactions.
local function deleteSpawnedPeds()
  for _, info in ipairs(spawnedPeds) do
    if info.ped and DoesEntityExist(info.ped) then
      DeleteEntity(info.ped)
    end
  end
  spawnedPeds = {}
end

-- Ped configurations for each team
local pedSpawns = {
  red = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
    -- Added prestige shop ped for the red team.  We reuse the autoshop worker
    -- model so that it remains allowed by the server's ped blocker script.
    -- The offset positions the prestige ped a few metres away from the
    -- other shop peds to avoid overlap.  Interacting with this ped will
    -- trigger the prestige menu via the koth:openPrestigeMenu event.
    { model = `s_m_m_autoshop_02`, offset = vector3( 0.0,  6.0, 0.0), text = 'Click [E] To Open Prestige Shop', event = 'koth:openPrestigeMenu' },
  },
  blue = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
    -- Prestige shop ped for the blue team
    { model = `s_m_m_autoshop_02`, offset = vector3( 0.0,  6.0, 0.0), text = 'Click [E] To Open Prestige Shop', event = 'koth:openPrestigeMenu' },
  },
  green = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
    -- Prestige shop ped for the green team
    { model = `s_m_m_autoshop_02`, offset = vector3( 0.0,  6.0, 0.0), text = 'Click [E] To Open Prestige Shop', event = 'koth:openPrestigeMenu' },
  },
}

-- Spawn peds for the current team spawn positions.  This helper is used
-- during map rotations to respawn team selection peds at the updated
-- coordinates.  It follows the same logic as the initial ped spawn that
-- occurs on resource start.
local function spawnPedsForCurrentSpawns()
  deleteSpawnedPeds()
  for team, list in pairs(pedSpawns) do
    local sv = teamSpawns[team]
    if sv then
      local basePos = vector3(sv.x, sv.y, sv.z)
      print(('[KOTH] Respawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))
      for _, info in ipairs(list) do
        if IsModelValid(info.model) and IsModelInCdimage(info.model) then
          RequestModel(info.model)
          local timeout = GetGameTimer() + 5000
          while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
            Citizen.Wait(1)
          end
          local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
          local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
          local finalZ = foundGround and groundZ or basePos.z
          local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)
          if DoesEntityExist(ped) then
            SetEntityHeading(ped, sv.heading or 0.0)
            FreezeEntityPosition(ped, true)
            SetEntityInvincible(ped, true)
            SetBlockingOfNonTemporaryEvents(ped, true)
            -- Record which team this ped belongs to so we can restrict
            -- interaction to players on the same team.  Without this
            -- metadata other teams could open these peds' menus.
            table.insert(spawnedPeds, {
              ped = ped,
              text = info.text,
              event = info.event,
              team = team
            })
            print(('[KOTH] Respawned %s ped for team %s'):format(info.model, team))
          end
          SetModelAsNoLongerNeeded(info.model)
        else
          print(('[KOTH] Invalid model %s for team %s'):format(info.model, team))
        end
      end
    else
      print(('[KOTH] WARNING: No spawn data for team %s when respawning peds'):format(team))
    end
  end
end

--[[
  Update the client's spawn table for each team.  The server emits the
  'koth:updateTeamSpawns' event when a map rotates and when a client
  initially joins the resource.  Without this handler, the client
  continues to use whatever spawn coordinates were defined in
  teamSpawns at load time, which may be a placeholder set.  Upon
  receiving new spawn data we overwrite the teamSpawns table and
  respawn the team selection peds so that they appear at the correct
  locations.  This also ensures the death system spawns are kept in
  sync because it registers its own handler for this event in
  client_death.lua.
--]]
RegisterNetEvent('koth:updateTeamSpawns')
AddEventHandler('koth:updateTeamSpawns', function(spawns)
  if spawns and type(spawns) == 'table' then
    teamSpawns = spawns
    -- If the ped spawning function exists, respawn team selection peds
    -- at the new coordinates.  spawnPedsForCurrentSpawns is defined in
    -- this file so it will be available when this handler is executed.
    spawnPedsForCurrentSpawns()
  end
end)

-- 3D text helper function
local function Draw3DText(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z + 1.0)
  if onScreen then
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextCentre(true)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (#text) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 120)
  end
end

-- Spawn peds when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  Citizen.CreateThread(function()
    print('[KOTH] Starting ped spawning...')
    Citizen.Wait(3000) -- Wait for everything to load

    for team, list in pairs(pedSpawns) do
      local sv = teamSpawns[team]
      if not sv then
        print('[KOTH] WARNING: No spawn data for team ' .. team)
      else
        local basePos = vector3(sv.x, sv.y, sv.z)
        print(('[KOTH] Spawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))

        for _, info in ipairs(list) do
          -- Validate model
          if not (IsModelValid(info.model) and IsModelInCdimage(info.model)) then
            print(('[KOTH] SKIP: Invalid model %s for team %s'):format(info.model, team))
          else
            -- Load model
            RequestModel(info.model)
            local timeout = GetGameTimer() + 5000
            while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
              Citizen.Wait(1)
            end

            if not HasModelLoaded(info.model) then
              print(('[KOTH] FAIL: Could not load model %s'):format(info.model))
            else
              -- Find ground level
              local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
              local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
              local finalZ = foundGround and groundZ or basePos.z

              -- Spawn ped
              local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)

              if DoesEntityExist(ped) then
                SetEntityHeading(ped, sv.heading or 0.0)
                FreezeEntityPosition(ped, true)
                SetEntityInvincible(ped, true)
                SetBlockingOfNonTemporaryEvents(ped, true)

            -- When spawning peds, record which team they belong to.  This
            -- allows the interaction loop to restrict access so that
            -- players can only talk to peds belonging to their own
            -- team.  Without storing the team identifier here, every
            -- ped would be interactable by anyone which is not desired.
            table.insert(spawnedPeds, {
              ped = ped,
              text = info.text,
              event = info.event,
              team = team -- store the team this ped belongs to
            })

                print(('[KOTH] SUCCESS: Spawned %s ped for team %s'):format(info.model, team))
                -- After inserting into spawnedPeds, annotate this ped
                -- with its team so that only the matching team can
                -- interact with it later.  Without this, players
                -- from other teams could open menus from another
                -- team’s ped.
                spawnedPeds[#spawnedPeds].team = team
              else
                print(('[KOTH] FAIL: CreatePed failed for %s'):format(info.model))
              end

              SetModelAsNoLongerNeeded(info.model)
            end
          end
        end
      end
    end

    print(('[KOTH] Ped spawning complete. Total peds: %d'):format(#spawnedPeds))
  end)
end)

-- =====================================================
-- PREVIOUS LOADOUT PROMPT
-- =====================================================
-- Display a small on‑screen prompt allowing the player to repurchase
-- their previously bought weapon and attachments after dying.  The
-- prompt only appears when the player is near their team's spawn and
-- does not currently have the previous weapon equipped.  Pressing the
-- G key will automatically purchase the weapon and any recorded
-- attachments again (if the player has enough money).
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if previousLoadout.weapon then
      local ped = PlayerPedId()
      -- Only show prompt if the player is alive
      if not IsEntityDead(ped) then
        -- Ensure the player has selected a team and we know its spawn
        if playerTeam and teamSpawns and teamSpawns[playerTeam] then
          local spawn = teamSpawns[playerTeam]
          local px, py, pz = table.unpack(GetEntityCoords(ped))
          -- Compute distance to team spawn
          local dist = #(vector3(px, py, pz) - vector3(spawn.x, spawn.y, spawn.z))
          -- Show within 15 units; hide otherwise
          if dist <= 15.0 then
            -- Only show if the player no longer has the previous weapon equipped
            if GetSelectedPedWeapon(ped) ~= GetHashKey(previousLoadout.weapon) then
              -- Draw prompt near bottom of the screen
              SetTextFont(4)
              SetTextScale(0.35, 0.35)
              SetTextColour(255, 255, 255, 255)
              SetTextCentre(true)
              BeginTextCommandDisplayText("STRING")
              AddTextComponentSubstringPlayerName("Click [G] To Purchase Your Previous Loadout")
              EndTextCommandDisplayText(0.5, 0.875)

              -- Detect G key press (control index 47)
              if IsControlJustPressed(0, 47) then
                -- Purchase the previous weapon again and then, once it is
                -- equipped, purchase all recorded attachments.  We
                -- perform the attachment purchases in a separate
                -- coroutine that waits until the weapon is actually
                -- equipped before applying attachments.  This avoids
                -- attachments failing to apply if they are purchased
                -- before the weapon swap completes.
                local weaponName = previousLoadout.weapon
                local weaponPrice = previousLoadout.weaponPrice or 0
                local purchaseType = previousLoadout.purchaseType or (previousLoadout.owned and 'buy' or 'rent')

                -- Copy attachments table to avoid mutation during
                -- asynchronous purchase
                local attachmentsToBuy = {}
                if previousLoadout.attachments then
                  for idx, att in ipairs(previousLoadout.attachments) do
                    attachmentsToBuy[idx] = {
                      name = att.name,
                      component = att.component,
                      price = att.price
                    }
                  end
                end


                -- Determine if we need to preserve attachments.  Only set
                -- the repurchasing flag if we actually have attachments
                -- to reapply.  If there are no attachments recorded,
                -- resetting the attachments table is desired.
                if #attachmentsToBuy > 0 then
                  isRepurchasingLoadout = true
                else
                  isRepurchasingLoadout = false
                end

                -- Trigger the weapon purchase/rental for the player's current class
                TriggerServerEvent('koth:selectLoadout', currentClass or 'assault', weaponName, weaponPrice, purchaseType)

                -- Start a new thread to handle attachment purchases after the
                -- weapon has been given to the player
                if #attachmentsToBuy > 0 then
                  Citizen.CreateThread(function()
                    local hash = GetHashKey(weaponName)
                    local attempts = 0
                    -- Wait up to ~5 seconds (25 attempts) for the weapon
                    -- to be equipped
                    while attempts < 25 do
                      Citizen.Wait(200)
                      if GetSelectedPedWeapon(PlayerPedId()) == hash then
                        break
                      end
                      attempts = attempts + 1
                    end
                    -- Purchase each attachment sequentially with a small
                    -- delay between purchases to ensure proper application
                    for _, att in ipairs(attachmentsToBuy) do
                      TriggerServerEvent('koth:purchaseAttachment', {
                        name = att.name,
                        component = att.component,
                        price = att.price
                      })
                      Citizen.Wait(200)
                    end
                    -- Finished repurchasing attachments; clear repurchase flag
                    isRepurchasingLoadout = false
                  end)
                end
              end
            end
          end
        end
      end
    end
  end
end)

-- Interaction loop for peds
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, info in ipairs(spawnedPeds) do
      if DoesEntityExist(info.ped) then
        local pedCoords = GetEntityCoords(info.ped)
        local distance = #(playerCoords - pedCoords)
        -- Determine which team this ped belongs to by checking its stored
        -- team field or inferring it from proximity to known team spawn
        -- locations.  The ped should only be interactable if its team
        -- matches the player's team.
        local pedTeam = info.team
        if not pedTeam then
          -- Infer team by finding the nearest team spawn within 30 units
          local closestTeam = nil
          local closestDist = nil
          for tName, spawn in pairs(teamSpawns) do
            local d = #(pedCoords - vector3(spawn.x, spawn.y, spawn.z))
            if closestDist == nil or d < closestDist then
              closestDist = d
              closestTeam = tName
            end
          end
          pedTeam = closestTeam
        end
        -- Only allow interaction with peds from the player's team
        if pedTeam == playerTeam then
          if distance < 10.0 then
            Draw3DText(pedCoords.x, pedCoords.y, pedCoords.z, info.text)
            if distance < 1.5 and IsControlJustReleased(0, 38) then -- E key
              print(('[KOTH] Player interacted with ped: %s'):format(info.event))
              TriggerEvent(info.event)
            end
          end
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- Listen for kill feed messages from the server.  When a player is
-- killed, the server broadcasts a formatted message containing the
-- killer and victim names with team colours.  Display this message
-- using the feed ticker on the HUD.  This integrates seamlessly
-- with the existing notification system.
-- Kill feed event handler.  This handler forwards kill feed
-- notifications from the server to the NUI so that the kill feed
-- can be rendered in the custom UI overlay (top right corner).  The
-- server sends a structured table containing the killer and victim
-- names and their teams.  See ui.html/script.js for rendering logic.
RegisterNetEvent('koth:killFeed')
AddEventHandler('koth:killFeed', function(data)
    if type(data) ~= 'table' then return end
    -- Ensure required fields are present
    local killerName = data.killerName or 'Unknown'
    local victimName = data.victimName or 'Unknown'
    local killerTeam = data.killerTeam or 'neutral'
    local victimTeam = data.victimTeam or 'neutral'
    SendNUIMessage({
        action = 'killFeed',
        killerName = killerName,
        victimName = victimName,
        killerTeam = killerTeam,
        victimTeam = victimTeam
    })
end)

-- SAFE ZONE SYSTEM
local inSafeZone = false
-- Radius of team safe zones.  The server owner requested that team
-- spawns have much smaller protection areas so they do not cover
-- large portions of the map.  We reduce the radius from 250.0 to
-- 75.0.  You can adjust this value to further increase or decrease
-- the size of the safe zones.
local safeZoneRadius = 75.0
local teamBlips = {} -- Store map blips

-- Map of serverId -> team for all connected players.  Updated
-- regularly by the server to allow clients to determine which
-- players are on their team.  When set, this table contains
-- entries like [123] = 'red', [456] = 'blue', etc.  The keys are
-- server IDs (numbers) and the values are the team names (strings).
local teamMembers = {}

-- Blip handles for teammates on the minimap.  Each key is a server
-- ID and each value is a table containing a blip handle and the ped
-- handle it was created for.  Each entry looks like
--   [serverId] = { blip = blipHandle, ped = pedHandle }
-- These blips are created for players on the same team as the local
-- player and removed when a player leaves the team or disconnects.
local teammateBlips = {}

-- Zone colors for each team
local zoneColors = {
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

-- Map blip colors (GTA blip color IDs)
local blipColors = {
  red = 1,    -- Red
  blue = 3,   -- Blue
  green = 2   -- Green
}

-- Safe zone check thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local wasInSafeZone = inSafeZone
      inSafeZone = false

      -- Check all team spawns for safe zones
      for team, spawn in pairs(teamSpawns) do
        local distance = #(playerCoords - vector3(spawn.x, spawn.y, spawn.z))

        if distance <= safeZoneRadius then
          -- Determine if this is our own team's safe zone
          if team ~= playerTeam then
            -- If the player drives into another team's spawn, delete their
            -- vehicle to prevent griefing.  Only perform this check if
            -- the player is in a vehicle.
            if IsPedInAnyVehicle(playerPed, false) then
              local veh = GetVehiclePedIsIn(playerPed, false)
              if DoesEntityExist(veh) then
                -- Remove player from vehicle and delete it
                TaskLeaveVehicle(playerPed, veh, 16)
                Citizen.Wait(100)
                DeleteEntity(veh)
                -- Notify player
                BeginTextCommandThefeedPost("STRING")
                AddTextComponentSubstringPlayerName("You cannot drive into enemy spawn zones. Vehicle removed.")
                EndTextCommandThefeedPostTicker(false, true)
              end
            end
          end
          inSafeZone = true

          -- Disable PVP in safe zones
          SetCanAttackFriendly(playerPed, false, false)
          NetworkSetFriendlyFireOption(false)

          -- Removed safe zone marker to prevent flashing

          -- Show safe zone notification on entering any safe zone
          if not wasInSafeZone then
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Entered Safe Zone - PVP Disabled")
            EndTextCommandThefeedPostTicker(false, true)
          end

          break
        end
      end

      -- Re-enable PVP when leaving safe zone
      if not inSafeZone and wasInSafeZone then
        SetCanAttackFriendly(playerPed, true, false)
        NetworkSetFriendlyFireOption(true)

        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Left Safe Zone - PVP Enabled")
        EndTextCommandThefeedPostTicker(false, true)
      end
    end

    Citizen.Wait(100)
  end
end)

-- KOTH ZONE SYSTEM
local kothZone = {
  x = 2842.4216,
  y = 2864.8088,
  z = 62.5975,
  -- Triple the default capture radius to encourage larger fights.  The new
  -- diameter is 1500m (750m radius) as per server owner's request.
  radius = 750.0,
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second when capturing
  playersInZone = {}, -- Track players in zone by team
  blip = nil, -- Map blip for the zone
  centerBlip = nil -- Center marker blip
}

-- Set to true after the server sends the real zone via koth:rotateMap
local receivedServerZone = false

local inKothZone = false
local kothZoneColors = {
  neutral = { r = 128, g = 128, b = 128, a = 50 }, -- Grey
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

local kothBlipColors = {
  neutral = 8, -- Grey
  red = 1,     -- Red
  blue = 3,    -- Blue
  green = 2    -- Green
}

-- Create KOTH zone blip
local function createKothBlip()
  print('[KOTH] Creating KOTH zone blip...')

  -- Create radius blip
  kothZone.blip = AddBlipForRadius(kothZone.x, kothZone.y, kothZone.z, kothZone.radius)
  SetBlipColour(kothZone.blip, kothBlipColors.neutral)
  SetBlipAlpha(kothZone.blip, 128)

  -- Create center blip
  kothZone.centerBlip = AddBlipForCoord(kothZone.x, kothZone.y, kothZone.z)
  SetBlipSprite(kothZone.centerBlip, 437) -- King crown icon
  SetBlipColour(kothZone.centerBlip, kothBlipColors.neutral)
  SetBlipScale(kothZone.centerBlip, 1.5) -- Larger than team blips
  SetBlipAsShortRange(kothZone.centerBlip, false) -- Always visible

  -- Set blip name
  BeginTextCommandSetBlipName("STRING")
  AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  EndTextCommandSetBlipName(kothZone.centerBlip)

  print(('[KOTH] Created KOTH zone blip at %.2f, %.2f'):format(kothZone.x, kothZone.y))
end

-- Create map blips for team zones
local function createTeamBlips()
  print('[KOTH] Creating team zone blips...')

  -- Defensive cleanup: remove any existing team and KOTH blips first so we never
  -- accumulate duplicate circles/icons across rounds or restarts.
  if teamBlips then
    for _, blips in pairs(teamBlips) do
      if blips.radius and DoesBlipExist(blips.radius) then RemoveBlip(blips.radius) end
      if blips.center and DoesBlipExist(blips.center) then RemoveBlip(blips.center) end
    end
  end
  teamBlips = {}

  if kothZone and kothZone.blip and DoesBlipExist(kothZone.blip) then RemoveBlip(kothZone.blip) end
  if kothZone and kothZone.centerBlip and DoesBlipExist(kothZone.centerBlip) then RemoveBlip(kothZone.centerBlip) end
  if kothZone then
    kothZone.blip = nil
    kothZone.centerBlip = nil
  end

  for team, spawn in pairs(teamSpawns) do
    local blip = AddBlipForRadius(spawn.x, spawn.y, spawn.z, safeZoneRadius)

    -- Set blip properties
    SetBlipColour(blip, blipColors[team] or 0)
    SetBlipAlpha(blip, 128) -- Semi-transparent

    -- Create center blip for the team
    local centerBlip = AddBlipForCoord(spawn.x, spawn.y, spawn.z)
    SetBlipSprite(centerBlip, 84) -- Shield icon
    SetBlipColour(centerBlip, blipColors[team] or 0)
    SetBlipScale(centerBlip, 1.0)
    SetBlipAsShortRange(centerBlip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName(team:upper() .. " Team Safe Zone")
    EndTextCommandSetBlipName(centerBlip)

    -- Store blips for cleanup
    teamBlips[team] = {
      radius = blip,
      center = centerBlip
    }

    print(('[KOTH] Created %s team blip at %.2f, %.2f'):format(team, spawn.x, spawn.y))
  end

  -- Create KOTH zone blip
  createKothBlip()
end
  -- Blips are created after we receive the authoritative zone from the server.
  -- See the onClientResourceStart handler below which waits for receivedServerZone
  -- and then calls createTeamBlips().

-- Initialize blips when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  -- Wait until we receive the authoritative zone from the server to avoid
  -- drawing a wrong default circle after relog/restart. Fallback after 10s.
  Citizen.CreateThread(function()
    local waited = 0
    while not receivedServerZone and waited < 10000 do
      Citizen.Wait(100)
      waited = waited + 100
    end
    createTeamBlips()
  end)
end)

-- Update KOTH zone blip color and name
local function updateKothBlip()
  if not DoesBlipExist(kothZone.blip) or not DoesBlipExist(kothZone.centerBlip) then
    return
  end

  local team = kothZone.controllingTeam or 'neutral'
  local color = kothBlipColors[team] or kothBlipColors.neutral

  -- Update colors
  SetBlipColour(kothZone.blip, color)
  SetBlipColour(kothZone.centerBlip, color)

  -- Update name
  BeginTextCommandSetBlipName("STRING")
  if team == 'neutral' then
    AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  else
    AddTextComponentSubstringPlayerName(("KOTH - Quarry (%s)"):format(team:upper()))
  end
  EndTextCommandSetBlipName(kothZone.centerBlip)
end

-- KOTH zone tracking thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      -- Use 2D distance (ignore Z) so players above/below ground are still counted
      local dx = (playerCoords.x or playerCoords[1]) - kothZone.x
      local dy = (playerCoords.y or playerCoords[2]) - kothZone.y
      local distSq = dx*dx + dy*dy
      local radius = kothZone.radius or 0.0
      local inZoneNow = (not IsEntityDead(playerPed)) and (distSq <= (radius * radius))

      local wasInZone = inKothZone
      inKothZone = inZoneNow

      -- Player entered zone
      if inKothZone and not wasInZone then
        print('[KOTH] Player entered KOTH zone')
        TriggerServerEvent('koth:playerEnteredZone', playerTeam)
      end

      -- Player left zone (includes death inside zone)
      if not inKothZone and wasInZone then
        print('[KOTH] Player left KOTH zone')
        TriggerServerEvent('koth:playerLeftZone', playerTeam)
      end

      -- Removed KOTH zone marker to prevent flashing
    end

    Citizen.Wait(100)
  end
end)

-- Update zone status from server
RegisterNetEvent('koth:updateZoneStatus', function(status)
  kothZone.controllingTeam = status.controllingTeam
  kothZone.captureProgress = status.captureProgress
  kothZone.captureThreshold = status.captureThreshold
  kothZone.playersInZone = status.playersInZone
  kothZone.dominantTeam = status.dominantTeam
  kothZone.isContested = status.isContested

  -- Update blip color
  updateKothBlip()

  -- No UI updates - everything is in chat now
end)

-- Zone control changed event
RegisterNetEvent('koth:zoneControlChanged', function(newTeam)
  print(('[KOTH] Zone control changed to: %s'):format(newTeam or 'neutral'))
  kothZone.controllingTeam = newTeam
  updateKothBlip()
end)

-- Update team counts for HUD
RegisterNetEvent('koth:updateTeamCounts', function(counts)
  print('[KOTH] Received team counts from server:', json.encode(counts))
  SendNUIMessage({
    action = 'updateTeamCounts',
    counts = counts
  })
  print('[KOTH] Sent team counts to NUI')
end)

--[[
Handle updates to the player/team mapping.  The server broadcasts
playerTeams (mapping of server ID -> team) whenever team counts are
updated.  When this event fires, update our local teamMembers table
and refresh teammate blips.  Blips are created only for players on
the same team as the local player and removed for all others.
]]
RegisterNetEvent('koth:updateTeamMembers', function(serverTeamMap)
  -- Replace our local table entirely to avoid stale entries.  Convert
  -- any string keys to numbers to ensure they work correctly with
  -- GetPlayerFromServerId (which expects a number).  Without
  -- conversion, JSON encoding on the server may send numeric keys as
  -- strings, causing lookups to fail on the client.
  teamMembers = {}
  if serverTeamMap then
    for id, team in pairs(serverTeamMap) do
      local numericId = tonumber(id) or id
      teamMembers[numericId] = team
    end
  end
  updateTeammateBlips()
end)

--
-- Update teammate blips based on the current teamMembers mapping.  This
-- function iterates over all known players and creates blips for
-- those on the same team as the local player.  Blips for players
-- who have left the team or disconnected are removed.  Blip colours
-- are set based on the team (red, green or blue) using GTA's built-in
-- colour indices: 1 (red), 2 (green), 3 (blue).  Teammate blips are
-- updated periodically in a separate thread to follow player
-- movement.
--
function updateTeammateBlips()
  -- Determine our own server ID and team
  local myServerId = GetPlayerServerId(PlayerId())
  local myTeam = teamMembers[myServerId]
  -- If we don't have a team yet, remove any existing teammate blips
  if not myTeam then
    -- Remove all existing teammate blips if we don't currently
    -- belong to a team.  Each entry stores a table with a blip
    -- handle, so remove the blip field.
    for id, data in pairs(teammateBlips) do
      if data and data.blip and DoesBlipExist(data.blip) then
        RemoveBlip(data.blip)
      end
    end
    teammateBlips = {}
    return
  end
  -- Remove blips that no longer correspond to teammates.  Also remove
  -- blips whose underlying entity is no longer valid (e.g., the
  -- player has disconnected).  We check membership and connectivity
  -- here.  Entries in teammateBlips store both the blip handle and
  -- the ped handle it was created for.
  for id, data in pairs(teammateBlips) do
    local playerIndex = GetPlayerFromServerId(id)
    if teamMembers[id] ~= myTeam or not NetworkIsPlayerActive(playerIndex) then
      if data.blip and DoesBlipExist(data.blip) then
        RemoveBlip(data.blip)
      end
      teammateBlips[id] = nil
    end
  end
  -- Create or update blips for each player on the same team.  If the
  -- player's ped has changed (e.g., due to death/respawn), remove
  -- the old blip and create a new one for the new ped.  We skip
  -- ourselves (no blip for the local player).
  for id, team in pairs(teamMembers) do
    if id ~= myServerId and team == myTeam then
      local playerIndex = GetPlayerFromServerId(id)
      if NetworkIsPlayerActive(playerIndex) then
        local ped = GetPlayerPed(playerIndex)
        if ped and DoesEntityExist(ped) then
          local existing = teammateBlips[id]
          if existing then
            -- If the ped has changed, recreate the blip
            if existing.ped ~= ped then
              if existing.blip and DoesBlipExist(existing.blip) then
                RemoveBlip(existing.blip)
              end
              existing = nil
            end
          end
          if not existing then
            -- Create new blip for this ped
            local blip = AddBlipForEntity(ped)
            SetBlipSprite(blip, 1) -- small round dot
            -- Set colour based on team
            local colour = 0
            if team == 'red' then colour = 1
            elseif team == 'green' then colour = 2
            elseif team == 'blue' then colour = 3
            end
            SetBlipColour(blip, colour)
            SetBlipScale(blip, 0.8)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentSubstringPlayerName("Teammate")
            EndTextCommandSetBlipName(blip)
            teammateBlips[id] = { blip = blip, ped = ped }
          else
            -- Ensure the colour is correct if the team changed (unlikely)
            local blip = existing.blip
            if DoesBlipExist(blip) then
              local colour = 0
              if team == 'red' then colour = 1
              elseif team == 'green' then colour = 2
              elseif team == 'blue' then colour = 3
              end
              SetBlipColour(blip, colour)
            end
          end
        end
      end
    end
  end
end

-- Background thread to update teammate blip positions.  Without
-- repositioning, blips would remain at the location where they were
-- created.  This loop runs once per second to update the blip
-- coordinates for all tracked teammates.  Adjust the wait time if
-- smoother updates are desired.
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)
    -- Refresh teammate blips to handle ped changes and membership
    updateTeammateBlips()
    -- Update blip positions for all teammates
    for id, data in pairs(teammateBlips) do
      local blip = data.blip
      local ped = data.ped
      if blip and ped and DoesEntityExist(ped) then
        local coords = GetEntityCoords(ped)
        SetBlipCoords(blip, coords.x, coords.y, coords.z)
      end
    end
  end
end)

-- Update zone points for HUD
RegisterNetEvent('koth:updateZonePoints', function(points)
  SendNUIMessage({
    action = 'updateZonePoints',
    points = points
  })
end)

-- PVP SYSTEM
RegisterNetEvent('koth:enablePVP', function()
  print('[KOTH] Enabling PVP')
  local playerPed = PlayerPedId()
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
end)

--
-- Friendly fire prevention: block damage between players on the same team.
--
-- Listen for the CEventNetworkEntityDamage game event, which fires
-- whenever one entity damages another.  When a player attacks a
-- teammate, cancel the event to prevent any damage being applied.
-- This preserves full PVP capability against enemies while making
-- team killing impossible.
AddEventHandler('gameEventTriggered', function(eventName, args)
  -- Only intercept damage events
  if eventName ~= 'CEventNetworkEntityDamage' then return end
  if not args or #args < 2 then return end
  local victim = args[1]
  local attacker = args[2]
  -- Ensure both victim and attacker exist and are peds
  if not victim or not attacker then return end
  if not DoesEntityExist(victim) or not DoesEntityExist(attacker) then return end
  if not IsEntityAPed(victim) or not IsEntityAPed(attacker) then return end
  -- Only handle player vs player damage
  if not IsPedAPlayer(victim) or not IsPedAPlayer(attacker) then return end
  -- Determine network indices and server IDs
  local victimPlayer = NetworkGetPlayerIndexFromPed(victim)
  local attackerPlayer = NetworkGetPlayerIndexFromPed(attacker)
  -- Exclude self damage and ensure both players are valid
  if victimPlayer == attackerPlayer or victimPlayer < 0 or attackerPlayer < 0 then return end
  local victimServerId = GetPlayerServerId(victimPlayer)
  local attackerServerId = GetPlayerServerId(attackerPlayer)
  if not victimServerId or not attackerServerId then return end
  -- Look up team assignments; teamMembers is populated via
  -- koth:updateTeamMembers event from the server.  If either player
  -- doesn't have a team assigned yet, do nothing to allow damage.
  local vTeam = teamMembers and teamMembers[victimServerId] or nil
  local aTeam = teamMembers and teamMembers[attackerServerId] or nil
  if not vTeam or not aTeam then return end
  -- Cancel damage and restore the victim's state if both players are on the same team.
  -- CancelEvent alone does not prevent the damage from being applied, so we
  -- explicitly restore the victim's health and armour from our cache.  For
  -- fatal hits (e.g. headshots), we additionally resurrect the local
  -- player to ensure that a critical hit does not kill a teammate.  According to
  -- community testing of CEventNetworkEntityDamage, the sixth element of
  -- the args table (args[6]) indicates whether the damage would kill the
  -- entity【260398389215136†L60-L71】.
  if vTeam == aTeam then
    -- Cancel subsequent handlers (may be redundant but kept for clarity)
    CancelEvent()
    -- Determine if the damage would be fatal.  args[6] == 1 indicates
    -- death, but fallback to checking IsEntityDead for reliability.
    local fatal = args[6]
    -- Retrieve previously recorded health/armour for this ped.  If
    -- unavailable, fall back to max health and zero armour.
    local data = lastHealths[victim]
    local maxHealth = GetEntityMaxHealth(victim)
    local restoreHealth = data and data.health or maxHealth
    local restoreArmour = data and data.armour or 0
    if restoreHealth > maxHealth then restoreHealth = maxHealth end
    -- If the damage would have killed the victim, they might already be
    -- dead by the time this handler runs.  Attempt to resurrect the
    -- local player before restoring their health/armour.  Each client
    -- handles their own resurrection; remote clients will not attempt
    -- to resurrect other players.
    if fatal == 1 or IsEntityDead(victim) then
      -- Only attempt resurrection if the victim is our local ped
      if victim == PlayerPedId() then
        local coords = GetEntityCoords(victim)
        local heading = GetEntityHeading(victim)
        -- Revive the local player on the spot.  The final two
        -- parameters (isNetwork and stayInVeh) control network sync
        -- and whether the player stays in their vehicle.
        NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, heading, true, true)
        -- Clear any tasks to avoid ragdoll or death animations
        ClearPedTasksImmediately(victim)
      end
    end
    -- Finally restore the health and armour to negate the damage
    SetEntityHealth(victim, restoreHealth)
    SetPedArmour(victim, restoreArmour)
  end
end)



-- Show level up popup
RegisterNetEvent('koth:levelUp', function(data)
  SendNUIMessage({
    action = 'showLevelUp',
    oldLevel = data.oldLevel,
    newLevel = data.newLevel
  })

  -- Play level up sound
  PlaySoundFrontend(-1, "RANK_UP", "HUD_AWARDS", true)
end)

--[[
  Bug report client integration.  When the server broadcasts a new
  report notification or sends the current list of reports, forward
  this information to the NUI so the admin panel can display it.
  Admin UI developers can listen for the `newReport` and
  `setReports` actions in HTML/JS to update their interfaces.
--]]
RegisterNetEvent('koth:newReportNotification')
AddEventHandler('koth:newReportNotification', function(report)
  SendNUIMessage({ action = 'newReport', report = report })
end)

RegisterNetEvent('koth:receiveReports')
AddEventHandler('koth:receiveReports', function(reports)
  SendNUIMessage({ action = 'setReports', reports = reports })
end)

--[[
  NUI callback for answering a bug report.  The admin panel should
  send the report id and response back to Lua, which then forwards
  the response to the server.  The server will handle notifying
  the reporting player and updating the logs.
--]]
RegisterNUICallback('answerReport', function(data, cb)
  if data and data.id and data.response then
    TriggerServerEvent('koth:answerReport', data.id, data.response)
  end
  cb('ok')
end)

-- NUI callback to close/remove a report from the pending list in the admin panel
RegisterNUICallback('closeReport', function(data, cb)
  if data and data.id then
    TriggerServerEvent('koth:closeReport', data.id)
  end
  cb('ok')
end)


--[[
  NUI callback for requesting the current bug report list.  The
  admin panel can call this when it loads the bug report screen to
  fetch all pending reports from the server.
--]]
RegisterNUICallback('getReports', function(data, cb)
  TriggerServerEvent('koth:getReports')
  cb('ok')
end)

--[[
  Handle self‑fix requests from the server.  When the player types
  `/fix` the server triggers koth:fixPlayer which kills the local
  ped.  This can be used when a player becomes stuck in the world
  (e.g. in physics glitches) to force a respawn.
--]]
RegisterNetEvent('koth:fixPlayer')
AddEventHandler('koth:fixPlayer', function()
  local ped = PlayerPedId()
  if ped and DoesEntityExist(ped) then
    -- Kill the player by setting their health to zero.  The death
    -- system will take over and handle respawning.
    SetEntityHealth(ped, 0)
  end
end)

-- Health bar update REMOVED as requested - user will integrate their own health system later

-- Initialize HUD when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  -- Request initial player data
  Citizen.SetTimeout(1000, function()
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- DATABASE SYNC SYSTEM - Periodic updates to keep HUD synced
Citizen.CreateThread(function()
  while true do
    -- Request fresh player data from database every 30 seconds
    if hasSelectedTeam then
      TriggerServerEvent('koth:requestPlayerData')
      print('[KOTH] Requesting periodic database sync for HUD')
    end

    Citizen.Wait(30000) -- 30 seconds
  end
end)

-- Force database sync when player performs actions that might change data
local function forceDatabaseSync()
  print('[KOTH] Forcing database sync after player action')
  TriggerServerEvent('koth:requestPlayerData')
end

-- Hook into purchase events to force sync
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- Force database sync after any purchase (successful or not)
  Citizen.SetTimeout(1000, function()
    forceDatabaseSync()
  end)

  -- If purchase failed, keep the shop open
  if not success then
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

--[[
  Development command to force the team selection screen open.  Disabled
  for production to prevent misuse.
]]
-- RegisterCommand('showteamselect', function()
--   print('[KOTH] Manually showing team selection UI')
--   -- Reset team selection state for testing
--   hasSelectedTeam = false
--   playerTeam = nil
--   DeleteResourceKvp('playerTeam')
--
--   SendNUIMessage({
--     action = 'showTeamSelect',
--     counts = { red = 0, blue = 0, green = 0 }
--   })
--   SetNuiFocus(true, true)
-- end, false)

--[[
  Development command to reset the team selection.  Disabled in
  production to keep the command list clean for players.
]]
-- RegisterCommand('resetteam', function()
--   print('[KOTH] Resetting team selection')
--   hasSelectedTeam = false
--   playerTeam = nil
--   DeleteResourceKvp('playerTeam')
--   print('[KOTH] Team selection reset - rejoin or use /showteamselect to pick a new team')
-- end, false)

--[[
  Debug command to test the kill reward popup.  Disabled for end users.
]]
-- RegisterCommand('testkillreward', function(source, args)
--   local inZone = args[1] == 'zone' or args[1] == 'true'
--   local xp = inZone and 150 or 50
--   local money = inZone and 150 or 50
--
--   print('[KOTH] Testing kill reward popup - In Zone:', inZone, 'XP:', xp, 'Money:', money)
--
--   -- Simulate receiving a kill reward
--   TriggerEvent('koth:showKillReward', {
--     xp = xp,
--     money = money,
--     inZone = inZone,
--     victimName = 'Test Player'
--   })
-- end, false)

--[[
  Debug command to manually sync data with the database.  Disabled
  for production use.
]]
-- RegisterCommand('syncdata', function()
--   print('[KOTH] Manually requesting database sync')
--   TriggerServerEvent('koth:requestPlayerData')
-- end, false)

--[[
  Debug command to test the kill reward popup directly.  Disabled for players.
]]
-- RegisterCommand('testkillpopup', function(source, args)
--   local inZone = args[1] == 'zone' or args[1] == 'true'
--   local xp = inZone and 150 or 50
--   local money = inZone and 150 or 50
--
--   print('[KOTH] Testing kill reward popup directly - In Zone:', inZone, 'XP:', xp, 'Money:', money)
--
--   -- Send directly to NUI to test popup display
--   SendNUIMessage({
--     action = 'showKillReward',
--     xp = xp,
--     money = money,
--     inZone = inZone,
--     victim = 'Test Player'
--   })
--
--   -- Also update player stats to simulate a real kill
--   if playerStats then
--     playerStats.money = (playerStats.money or 0) + money
--     playerStats.xp = (playerStats.xp or 0) + xp
--     playerStats.kills = (playerStats.kills or 0) + 1
--
--     -- Update HUD
--     SendNUIMessage({
--       action = 'updatePlayerData',
--       data = playerStats
--     })
--   end
--
--   print('[KOTH] Kill reward popup test sent to NUI')
-- end, false)

--[[
  Debug command to force the kill reward display multiple times.  Disabled.
]]
-- RegisterCommand('forcekillreward', function()
--   print('[KOTH] Forcing kill reward display...')
--
--   -- Send multiple attempts to ensure it shows
--   for i = 1, 3 do
--     Citizen.SetTimeout(i * 100, function()
--       SendNUIMessage({
--         action = 'showKillReward',
--         xp = 150,
--         money = 150,
--         inZone = true,
--         victim = 'Enemy Player'
--       })
--       print('[KOTH] Kill reward attempt', i, 'sent')
--     end)
--   end
-- end, false)

--[[
  Simple client test command used during development to show the UI.  Disabled.
]]
-- RegisterCommand('testui', function(source, args)
--   local inZone = args[1] == 'zone' or args[1] == 'true'
--   local xp = inZone and 150 or 50
--   local money = inZone and 150 or 50
--
--   print('[KOTH] Testing UI directly - Zone:', inZone, 'XP:', xp, 'Money:', money)
--
--   -- Test the showKillReward function directly
--   TriggerEvent('koth:showKillReward', {
--     xp = xp,
--     money = money,
--     inZone = inZone,
--     victimName = 'Test Victim'
--   })
--
--   print('[KOTH] UI test triggered')
-- end, false)

--[[
  Debug command to inspect the player's current vehicle decorators.  Disabled.
]]
-- RegisterCommand('checkvehicle', function()
--   local playerPed = PlayerPedId()
--   local vehicle = GetVehiclePedIsIn(playerPed, false)
--
--   if vehicle ~= 0 then
--     local hasDecorator = DecorExistOn(vehicle, "PlayerVehicle")
--     local decoratorValue = hasDecorator and DecorGetBool(vehicle, "PlayerVehicle") or false
--     local isPlayerOwned = Entity(vehicle) and Entity(vehicle).state.playerOwned or false
--
--     print('[KOTH] === Vehicle Debug Info ===')
--     print('[KOTH] Vehicle entity:', vehicle)
--     print('[KOTH] Has PlayerVehicle decorator:', hasDecorator)
--     print('[KOTH] PlayerVehicle decorator value:', decoratorValue)
--     print('[KOTH] Entity state playerOwned:', isPlayerOwned)
--     print('[KOTH] =========================')
--   else
--     print('[KOTH] You are not in a vehicle')
--   end
-- end, false)

print('[KOTH] Client loaded successfully')

-- ===============================
-- Map voting system (client side)
--
-- The server can initiate a map vote at the end of a round by sending
-- the 'koth:startMapVote' event along with a list of map names and
-- the duration of the vote in milliseconds.  While a vote is
-- active the client displays a simple menu listing all available
-- maps.  Players navigate the list using the up and down arrow
-- keys and cast their vote by pressing Enter.  The chosen map index
-- is sent back to the server via 'koth:submitVote'.  When the vote
-- concludes the server emits 'koth:endMapVote', at which point the
-- voting UI is hidden.

-- Whether a vote is currently active
local voteActive = false
-- Names of all maps provided by the server
local voteMaps = {}
-- The total duration of the current vote in milliseconds
local voteDuration = 0
-- Timestamp when the vote started (GetGameTimer)
local voteStartTime = 0
-- The index currently highlighted in the vote list (1‑based)
local voteCursor = 1
-- The player's most recent vote selection (may be nil until voted)
local voteSelection = nil

-- Whether the map vote UI is being shown via NUI (clickable voting)
local nuiVoteActive = false

-- Draw 3D text above teammates' heads.  This helper converts world
-- coordinates to screen space and scales the text based on distance and
-- camera FOV.  It draws the provided string centred at the computed
-- screen position.  Colours and styling are configured here.
local function DrawText3D(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  if not onScreen then return end
  local px, py, pz = table.unpack(GetGameplayCamCoords())
  local dist = #(vector3(px, py, pz) - vector3(x, y, z))
  local scale = (1.0 / dist) * 2.0
  local fov = (1.0 / GetGameplayCamFov()) * 100.0
  scale = scale * fov
  SetTextScale(0.35 * scale, 0.35 * scale)
  SetTextFont(0)
  SetTextProportional(true)
  SetTextColour(255, 255, 255, 215)
  SetTextDropshadow(0, 0, 0, 0, 255)
  SetTextEdge(2, 0, 0, 0, 150)
  SetTextDropShadow()
  SetTextOutline()
  SetTextEntry('STRING')
  SetTextCentre(true)
  AddTextComponentString(text)
  DrawText(_x, _y)
end

-- Background thread to draw teammate names above their heads.  Every
-- frame, determine the local player's team and iterate through the
-- teamMembers mapping to find other players on the same team.  If a
-- teammate's ped exists and is close enough, draw their name above
-- their head.  Adjust the offset to position the text above the
-- player's head.
Citizen.CreateThread(function()
  -- This background thread previously rendered a second set of nametags
  -- above teammates in plain white text.  It has been disabled to
  -- prevent duplicate labels.  The return statement below exits
  -- immediately when the thread starts.
  return
  --[[
  while true do
    Citizen.Wait(0)
    -- Determine our own server ID and team
    local myServerId = GetPlayerServerId(PlayerId())
    local myTeam = teamMembers and teamMembers[myServerId] or nil
    if myTeam then
      for id, team in pairs(teamMembers) do
        if id ~= myServerId and team == myTeam then
          local playerIndex = GetPlayerFromServerId(id)
          if NetworkIsPlayerActive(playerIndex) then
            local ped = GetPlayerPed(playerIndex)
            if ped and DoesEntityExist(ped) and not IsPedDeadOrDying(ped, true) then
              local coords = GetEntityCoords(ped)
              -- Raise the text above the head (1.2 units)
              DrawText3D(coords.x, coords.y, coords.z + 1.2, GetPlayerName(playerIndex))
            end
          end
        end
      end
    end
  end
  --]]
end)

-- Handle the start of a map vote.  Data contains `names` (array of
-- friendly map names) and `duration` (milliseconds).  Reset state
-- variables and begin rendering the vote UI.
RegisterNetEvent('koth:startMapVote')
AddEventHandler('koth:startMapVote', function(data)
  if not data then return end

  -- Always close any existing map vote UI before starting a new one.  This
  -- prevents stale state from a previous vote (e.g. a leftover
  -- countdown or selected option) from interfering with the next vote.  If
  -- a vote is already active, clear it and hide the panel.
  if nuiVoteActive then
    nuiVoteActive = false
    closeUIFocus()
    SendNUIMessage({ action = 'hideMapVote' })
  end
  if voteActive then
    voteActive = false
    voteMaps = {}
    voteSelection = nil
    voteCursor = 1
  end
  -- If the server provided a list of options with indices, use the NUI
  -- based voting interface.  Otherwise fall back to the original
  -- keyboard navigation overlay for legacy support.
  if data.options then
    nuiVoteActive = true
    voteActive = false
    -- Focus the cursor so players can click on the options.  For the
    -- voting UI we want to capture all input so the clicks are
    -- registered correctly, so do not keep input routed to the game.
    openUIFocus()
    -- Disable the input passthrough when showing the vote panel.  When
    -- SetNuiFocusKeepInput is true the game still receives inputs
    -- which can cause clicks not to register in the NUI.  Explicitly
    -- disable keepInput here so the mouse events go exclusively to
    -- the HTML panel.
    SetNuiFocusKeepInput(false)
    -- Store the mapping of indices to names for later reference.  This
    -- allows us to display the winning map's name when the vote ends.
    currentVoteOptions = {}
    for _, opt in ipairs(data.options) do
      -- Ensure both numeric and string keys map correctly.  Lua tables
      -- accept both number and string keys; storing both simplifies
      -- lookup without requiring type coercion later.
      currentVoteOptions[opt.index] = opt.name
      currentVoteOptions[tostring(opt.index)] = opt.name
    end
    -- Send options and duration to the UI.  The HTML side will render
    -- the buttons and start an internal timer.
    SendNUIMessage({
      action = 'showMapVote',
      options = data.options,
      duration = data.duration or 30000
    })
  else
    if not data.names then return end
    voteActive = true
    voteMaps = data.names or {}
    voteDuration = data.duration or 30000
    voteStartTime = GetGameTimer()
    voteCursor = 1
    voteSelection = nil
    -- For legacy keyboard voting, data.names only contains names.  If
    -- data.options is present (which includes indices), preserve the
    -- mapping from row position to map index so that we can send the
    -- correct map index to the server and display the winner name.
    currentVoteOptions = {}
    if data.options then
      for i, opt in ipairs(data.options) do
        -- Record the actual map index for each display position (i)
        currentVoteOptions[i] = opt.name
      end
    else
      -- Without options, we cannot derive indices; fallback to names only
      currentVoteOptions = {}
    end
    -- Display a brief notification so players know a vote has begun
    BeginTextCommandThefeedPost('STRING')
    AddTextComponentSubstringPlayerName('Map vote has started! Use Up/Down to select and Enter to vote.')
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- Handle the end of a map vote.  The server sends the winning index
-- and a table of vote counts.  Hide the voting UI and show a
-- summary message.
RegisterNetEvent('koth:endMapVote')
AddEventHandler('koth:endMapVote', function(data)
  -- Hide both NUI and overlay vote UIs
  if nuiVoteActive then
    nuiVoteActive = false
    -- Remove focus and hide the HTML overlay
    closeUIFocus()
    SendNUIMessage({ action = 'hideMapVote' })
  end
  voteActive = false
  local winner = data and data.winner or nil
  local votes = data and data.votes or nil
  -- Build a message summarising the vote outcome
  local msg
  -- If we have a mapping of indices to names, attempt to resolve the winner's name.
  -- This works for both the NUI interface and the legacy overlay.  If no
  -- mapping exists, fall back to a generic message.
  if winner and currentVoteOptions and (currentVoteOptions[winner] or currentVoteOptions[tostring(winner)]) then
    local name = currentVoteOptions[winner] or currentVoteOptions[tostring(winner)]
    msg = string.format('Next map: %s', name)
  else
    msg = 'Next map selected'
  end
  -- Clear stored options for the next vote
  currentVoteOptions = {}
  BeginTextCommandThefeedPost('STRING')
  AddTextComponentSubstringPlayerName(msg)
  EndTextCommandThefeedPostTicker(false, true)
end)

-- Receive updated vote counts during an active map vote.  The server
-- sends this event whenever a player casts a vote.  Forward the
-- counts to the NUI so the map vote interface can show live vote
-- totals.  We avoid doing any heavy processing here because the
-- counts may update rapidly when many players are voting.
RegisterNetEvent('koth:updateVoteCounts')
AddEventHandler('koth:updateVoteCounts', function(payload)
  if payload and payload.counts then
    -- Convert numeric keys to strings to preserve map indices when serialising to JSON
    local countsOut = {}
    for k, v in pairs(payload.counts) do
      countsOut[tostring(k)] = v
    end
    SendNUIMessage({ action = 'updateVoteCounts', counts = countsOut })
  end
end)

-- Thread to render the voting UI and handle input.  This runs every
-- frame and draws a semi‑transparent background with the list of
-- maps.  The currently selected map is highlighted.  Players
-- navigate with the up/down arrow keys and cast their vote with
-- Enter.  Their vote can be changed at any time before the vote
-- ends; submitting another vote will update the server.
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if voteActive then
      -- Calculate remaining time
      local now = GetGameTimer()
      local elapsed = now - voteStartTime
      local remaining = voteDuration - elapsed
      if remaining < 0 then remaining = 0 end
      -- Draw background rectangle
      local width = 0.4
      local height = 0.6
      local x = 0.3
      local y = 0.2
      DrawRect(x + width/2.0, y + height/2.0, width, height, 0, 0, 0, 180)
      -- Title text
      SetTextFont(0)
      SetTextScale(0.5, 0.5)
      SetTextColour(255, 255, 255, 255)
      SetTextCentre(false)
      BeginTextCommandDisplayText('STRING')
      AddTextComponentSubstringPlayerName('Vote for the next map')
      EndTextCommandDisplayText(x + 0.01, y + 0.01)
      -- Timer display
      local secs = math.floor(remaining / 1000)
      local timerStr = string.format('Time left: %ds', secs)
      BeginTextCommandDisplayText('STRING')
      AddTextComponentSubstringPlayerName(timerStr)
      EndTextCommandDisplayText(x + width - 0.15, y + 0.01)
      -- Draw map list
      local listYStart = y + 0.06
      local rowHeight = 0.025
      for i, name in ipairs(voteMaps) do
        local rowY = listYStart + (i - 1) * rowHeight
        -- Limit drawing to available height
        if rowY + rowHeight > y + height - 0.02 then
          break
        end
        -- Highlight selected row
        if i == voteCursor then
          DrawRect(x + width/2.0, rowY + rowHeight/2.0, width - 0.02, rowHeight, 50, 100, 200, 180)
        end
        -- Draw map name with its index
        SetTextFont(0)
        SetTextScale(0.45, 0.45)
        SetTextColour(255, 255, 255, 255)
        SetTextCentre(false)
        BeginTextCommandDisplayText('STRING')
        AddTextComponentSubstringPlayerName(string.format('%2d. %s', i, name))
        EndTextCommandDisplayText(x + 0.02, rowY + 0.002)
      end
      -- Handle input for navigation
      if IsControlJustPressed(0, 172) or IsControlJustPressed(0, 241) then -- Up arrow / scroll up
        voteCursor = voteCursor - 1
        if voteCursor < 1 then voteCursor = #voteMaps end
      elseif IsControlJustPressed(0, 173) or IsControlJustPressed(0, 242) then -- Down arrow / scroll down
        voteCursor = voteCursor + 1
        if voteCursor > #voteMaps then voteCursor = 1 end
      elseif IsControlJustPressed(0, 176) then -- Enter
        -- Submit vote to server
        TriggerServerEvent('koth:submitVote', voteCursor)
        voteSelection = voteCursor
        -- Provide immediate feedback
        BeginTextCommandThefeedPost('STRING')
        AddTextComponentSubstringPlayerName(string.format('You voted for %s', voteMaps[voteCursor]))
        EndTextCommandThefeedPostTicker(false, true)
      end
    end
  end
end)

-- Handle round end.  When a team reaches the point limit, the server
-- triggers this event with the winning team.  Show a notification,
-- fade the screen out and freeze the player until the map rotates.
RegisterNetEvent('koth:roundEnd')
AddEventHandler('koth:roundEnd', function(winningTeam)
  local ped = PlayerPedId()
  -- Display a message using the feed post ticker
  -- Display a generic message because the point limit may be configured on the server.
  -- Build an appropriate message depending on how the round ended
  local msg
  local upperTeam = type(winningTeam) == 'string' and string.upper(winningTeam) or ''
  if upperTeam == 'TIME' then
    msg = 'Time limit reached!\nMap vote starting soon...'
  else
    msg = string.format('Team %s reached the score limit!\nMap vote starting soon...', upperTeam)
  end
  BeginTextCommandThefeedPost('STRING')
  AddTextComponentSubstringPlayerName(msg)
  EndTextCommandThefeedPostTicker(false, true)

  -- Immediately clear all visible blips so the map is clean while the new round loads
  if teamBlips then
    for _, blips in pairs(teamBlips) do
      if blips.radius and DoesBlipExist(blips.radius) then RemoveBlip(blips.radius) end
      if blips.center and DoesBlipExist(blips.center) then RemoveBlip(blips.center) end
    end
    teamBlips = {}
  end
  if kothZone and kothZone.blip and DoesBlipExist(kothZone.blip) then RemoveBlip(kothZone.blip) end
  if kothZone and kothZone.centerBlip and DoesBlipExist(kothZone.centerBlip) then RemoveBlip(kothZone.centerBlip) end
  if kothZone then
    kothZone.blip = nil
    kothZone.centerBlip = nil
  end

  -- Freeze player during end round and until the map rotation completes
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
end)

-- Handle map rotation.  The server sends the new KOTH zone and team spawns.
-- Reset local variables, update blips, respawn peds and force players to
-- re-select teams.  Also fade the screen back in.
RegisterNetEvent('koth:rotateMap')
AddEventHandler('koth:rotateMap', function(data)
  print('[KOTH] Received map rotation data:', json.encode(data))

  -- Showcase the upcoming map using a high‑altitude camera rather than a
  -- black screen.  We position the camera roughly 500 units above the
  -- centre of the new KOTH zone and point it back down towards the zone.
  -- A notification is displayed while the camera pans, then we wait a
  -- moment before continuing with the normal map rotation logic.
  if data.zone then
    local zone = data.zone
    -- Create a scripted camera
    local cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    -- Position the camera high above the zone
    SetCamCoord(cam, zone.x, zone.y, zone.z + 500.0)
    -- Point the camera back at the centre of the capture zone
    PointCamAtCoord(cam, zone.x, zone.y, zone.z)
    -- Activate and render the camera
    SetCamActive(cam, true)
    RenderScriptCams(true, false, 0, true, true)
    -- Build a feed message describing the next map.  Use mapIndex if
    -- available, otherwise fall back to coordinates.
    local nextMsg
    if data.mapIndex then
      nextMsg = string.format('Next map is Zone %d', data.mapIndex)
    else
      nextMsg = string.format('Next map at X: %.0f, Y: %.0f', zone.x, zone.y)
    end
    BeginTextCommandThefeedPost('STRING')
    AddTextComponentSubstringPlayerName(nextMsg)
    EndTextCommandThefeedPostTicker(false, true)
    -- Allow players a few seconds to view the zone from above
    Citizen.Wait(3000)
    -- Clean up the camera and return control
    RenderScriptCams(false, false, 0, true, true)
    DestroyCam(cam, false)
  end

  -- Update KOTH zone coordinates and radius on the client
  if data.zone then
    kothZone.x = data.zone.x or kothZone.x
    kothZone.y = data.zone.y or kothZone.y
    kothZone.z = data.zone.z or kothZone.z
    kothZone.radius = data.zone.radius or kothZone.radius
    receivedServerZone = true
    -- Reset capture progress on client
    kothZone.controllingTeam = nil
    kothZone.captureProgress = 0.0
  end
  -- Update team spawn coordinates
  if data.spawns then
    teamSpawns = data.spawns
    -- Also update the death system's spawns so that players respawn at
    -- the new base after death.  We emit a client-side event rather than
    -- directly modifying DeathSystem.teamSpawns here since the death
    -- system lives in a separate script file.
    TriggerEvent('koth:updateTeamSpawns', teamSpawns)
  end
  -- Remove existing blips
  if teamBlips then
    for _, blips in pairs(teamBlips) do
      if blips.radius and DoesBlipExist(blips.radius) then RemoveBlip(blips.radius) end
      if blips.center and DoesBlipExist(blips.center) then RemoveBlip(blips.center) end
    end
    teamBlips = {}
  end
  -- Also remove any teammate blips (player markers).  When the map
  -- rotates or resets, all existing teammate blips should be cleared
  -- to avoid leaving stray markers on the minimap.  After clearing,
  -- reset the teamMembers and teammateBlips tables so they will be
  -- repopulated when a new team is selected.
  if teammateBlips then
    for id, data in pairs(teammateBlips) do
      if data and data.blip and DoesBlipExist(data.blip) then
        RemoveBlip(data.blip)
      end
    end
  end
  teammateBlips = {}
  teamMembers = {}
  if kothZone.blip and DoesBlipExist(kothZone.blip) then RemoveBlip(kothZone.blip) end
  if kothZone.centerBlip and DoesBlipExist(kothZone.centerBlip) then RemoveBlip(kothZone.centerBlip) end
  -- Respawn peds at new spawns
  spawnPedsForCurrentSpawns()
  -- Reset player team selection
  playerTeam = nil
  hasSelectedTeam = false
  DeleteResourceKvp('playerTeam')
  -- Keep the player frozen while they pick a new team
  local ped = PlayerPedId()
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
  -- Recreate blips after a short delay to ensure zone data is updated
  Citizen.SetTimeout(500, function()
    createTeamBlips()
  end)
  -- Request updated team counts and player data to refresh the UI
  Citizen.SetTimeout(1000, function()
    TriggerServerEvent('koth:requestCounts')
    TriggerServerEvent('koth:requestPlayerData')
  end)
  -- Fade the screen back in now that everything is ready
  DoScreenFadeIn(1000)
end)
